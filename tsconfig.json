{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "baseUrl": "./",
    "paths": {
      "@assets/*": ["./src/assets/*"],
      "@theme/*": ["./src/theme/*"],
      "@components/*": ["./src/components/*"],
      "@screens/*": ["./src/screens/*"],
      "@services/*": ["./src/services/*"],
      "@dtos/*": ["./src/dtos/*"],
      "@contexts/*": ["./src/contexts/*"],
      "@routes/*": ["./src/routes/*"],
      "@hooks/*": ["./src/hooks/*"],
      "@storage/*": ["./src/storage/*"],
      "@utils/*": ["./src/utils/*"]
    },
  }
}
