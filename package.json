{"name": "longev<PERSON><PERSON>", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/html-elements": "^0.5.1", "@expo/vector-icons": "~14.0.4", "@hookform/resolvers": "^3.2.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/drawer": "^7.1.1", "@tanstack/react-query": "^4.32.6", "@types/react": "~18.3.12", "axios": "^1.4.0", "date-fns": "^2.30.0", "expo": "^52.0.47", "expo-av": "~15.0.2", "expo-constants": "~17.0.8", "expo-font": "~13.0.4", "expo-linking": "~7.0.5", "expo-router": "~4.0.21", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "native-base": "^3.4.28", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.45.4", "react-native": "0.76.9", "react-native-device-info": "^11.1.0", "react-native-gesture-handler": "~2.20.2", "react-native-modalize": "^2.1.1", "react-native-onesignal": "^5.2.2", "react-native-portalize": "^1.0.7", "react-native-reanimated": "~3.16.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-svg-transformer": "^1.1.0", "react-native-webview": "13.12.5", "typescript": "~5.3.3", "yup": "^1.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "babel-plugin-module-resolver": "^5.0.0"}, "private": true}