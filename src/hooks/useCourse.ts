import { useQuery } from "@tanstack/react-query";
import { api } from "@services/api";
import { MyCourseStudent } from "src/@types/myCourses";

type GetMyCoursesResponse = {
	course: MyCourseStudent
}

type GetSearchMyCoursesStudentProps = {
	courseSecureId?: string
	subscriptionSecureId?: string
}

export async function getMyCourseContentStudent({ subscriptionSecureId, courseSecureId }: GetSearchMyCoursesStudentProps): Promise<GetMyCoursesResponse> {
	if (!courseSecureId || !subscriptionSecureId) return null

	const response = await api.get(`/v1/students/show-my-course`, {
		params: {
			courseSecureId,
			subscriptionSecureId
		}
	})

	return {
		course: response.data,
	}
}

export function useCourse({ subscriptionSecureId, courseSecureId }: GetSearchMyCoursesStudentProps) {
	return useQuery(['MyCourseContentStudent', subscriptionSecureId, courseSecureId], () => getMyCourseContentStudent({ subscriptionSecureId, courseSecureId }))
}
