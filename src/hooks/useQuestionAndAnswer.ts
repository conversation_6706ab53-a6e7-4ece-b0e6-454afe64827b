import { useInfiniteQuery } from "@tanstack/react-query";
import { api } from "@services/api";
import { ListQuestionAndAnswerProps } from "src/@types/questionAndAnswer";


type GetQuestionAndAnswerResponse = {
	questions: ListQuestionAndAnswerProps[];
	total: number;
	perPage: number;
	page: number;
	lastPage: number;
}

type GetQuestionAndAnswerProps = {
	lessonSecureId: string;
	search?: string;
	pageParam?: number;
	limit?: number;
	onlyMine?: boolean;
}

export async function getQuestionAndAnswer({ limit, lessonSecureId, onlyMine, pageParam }: GetQuestionAndAnswerProps): Promise<GetQuestionAndAnswerResponse> {
	const response = await api.get('/v1/students/question-and-answer', {
		params: {
			page: pageParam,
			limit,
			lessonSecureId,
			onlyMine
		}
	})

	return {
		questions: response.data.data,
		total: response.data.meta.total,
		perPage: response.data.meta.per_page,
		page: response.data.meta.current_page,
		lastPage: response.data.meta.last_page,
	}
}

export function useQuestionAndAnswer({ limit, lessonSecureId, onlyMine }: GetQuestionAndAnswerProps) {
	return useInfiniteQuery(
		['QuestionAndAnswer', limit, lessonSecureId, onlyMine],
		(props) => getQuestionAndAnswer({ limit, lessonSecureId, onlyMine, ...props }),
		{
			getPreviousPageParam: (params) => params.page > 1 ? params.page - 1 : undefined,
			getNextPageParam: (params) => (params.page < params.lastPage) ? params.page + 1 : undefined,
			select: (data) => ({
				pages: [...data.pages],
				pageParams: [...data.pageParams],
			}),
		}
	)
}

