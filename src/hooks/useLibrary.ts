import { useQuery } from "@tanstack/react-query";
import { api } from "@services/api";
import { LibraryItem } from "src/@types/library";

type GetLibraryResponseProps = {
  folders: LibraryItem[];
  contents: LibraryItem[];
}

async function getLibrary({ folderId }): Promise<GetLibraryResponseProps> {
  const url = folderId ? `/v1/students/library/items?folder=${folderId}` : '/v1/students/library/items'

  const response = await api.get(url)

  const folders = response.data.folders.map((item: LibraryItem) => ({
    ...item,
    type: 'folder'
  }))

  const contents = response.data.contents.map((item: LibraryItem) => ({
    ...item,
    type: 'content'
  }))


  return { folders, contents }

}

export function useLibrary({ folderId }) {
  return useQuery(['Library', folderId], () => getLibrary({ folderId }))
}
