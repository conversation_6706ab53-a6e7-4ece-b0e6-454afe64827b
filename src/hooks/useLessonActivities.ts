import { api } from "@services/api";
import { useInfiniteQuery, useQuery } from "@tanstack/react-query"
import { LessonActivityStudent } from "src/@types/myCourses";

type GetLessonActivityStudentResponse = {
	activities: LessonActivityStudent[]
	total: number;
	page: number;
	lastPage: number;
	perPage: number;
}

type GetLessonActivitiesProps = {
	lessonSecureId: string
	search?: string
	limit: number
	pageParam?: number
	subscriptionSecureId: string;
}

export async function getLessonActivities({ lessonSecureId, search, limit, pageParam, subscriptionSecureId }: GetLessonActivitiesProps): Promise<GetLessonActivityStudentResponse> {
	const response = await api.get(`/v1/students/activities-lesson/${lessonSecureId}`, {
		params: {
			search,
			limit,
			page: pageParam,
			subscriptionSecureId,
		}
	})

	return {
		total: response.data.meta.total,
		perPage: response.data.meta.per_page,
		page: response.data.meta.current_page,
		lastPage: response.data.meta.last_page,
		activities: response.data.data
	}
}

export function useLessonActivities({ lessonSecureId, search, limit, subscriptionSecureId }: GetLessonActivitiesProps) {
	return useInfiniteQuery(
		['LessonActivities', lessonSecureId, search, limit, subscriptionSecureId],
		() => getLessonActivities({ lessonSecureId, search, limit, subscriptionSecureId }),
		{
			getPreviousPageParam: (params) => params.page > 1 ? params.page - 1 : undefined,
			getNextPageParam: (params) => (params.page < params.lastPage) ? params.page + 1 : undefined,
			select: (data) => ({
				pages: [...data.pages],
				pageParams: [...data.pageParams],
			}),
		}
	)
}
