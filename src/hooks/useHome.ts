import { useQuery } from "@tanstack/react-query";
import { api } from "@services/api";
import { BannerHomeStudent, ListMyCoursesStudent, MyCourseStudent } from "src/@types/myCourses";

type GetHomeResponseProps = {
  categories: ListMyCoursesStudent[];
  lastVideosViewed: MyCourseStudent[];
  banners: BannerHomeStudent[];
}


async function getHome(): Promise<GetHomeResponseProps> {
  const response = await api.get('/v1/students/my-courses')

  return { ...response.data }
}

export function useHome() {
  return useQuery(['Home'], () => getHome())
}
