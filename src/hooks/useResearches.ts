import { useQuery } from "@tanstack/react-query";
import { api } from "@services/api";
import { ResearchProps } from "src/@types/researches";

type GetResearchesResponseProps = ResearchProps[]

type GetResearchesProps = {
	lessonSecureId?: string
	subscriptionSecureId?: string
}

export async function getResearches({ subscriptionSecureId, lessonSecureId }: GetResearchesProps): Promise<GetResearchesResponseProps> {
	if (!lessonSecureId || !subscriptionSecureId) return null

	const response = await api.get(`/v1/students/researchs-lesson`, {
		params: {
			lessonSecureId,
			subscriptionSecureId
		}
	})

	return response.data.data

}

export function useResearches({ subscriptionSecureId, lessonSecureId }: GetResearchesProps) {
	return useQuery(['Researches', subscriptionSecureId, lessonSecureId], () => getResearches({ subscriptionSecureId, lessonSecureId }))
}
