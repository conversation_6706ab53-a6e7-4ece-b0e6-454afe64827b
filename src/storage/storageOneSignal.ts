import AsyncStorage from "@react-native-async-storage/async-storage";

import { ONESIGNAL_STORAGE, ONESIGNAL_STORAGE_USER } from '@storage/storageConfig';

import { NotificationType } from "src/@types/Notification";

async function oneSignalStorageSave(notification: NotificationType) {
  await AsyncStorage.setItem(ONESIGNAL_STORAGE, JSON.stringify(notification))
}

async function oneSignalStorageGet() {
  const storage = await AsyncStorage.getItem(ONESIGNAL_STORAGE);

  const notification = storage ? JSON.parse(storage) : {};

  return notification
}

async function oneSignalStorageDelete() {
  await AsyncStorage.removeItem(ONESIGNAL_STORAGE);
}

////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////
async function userNotificationSave(userId: string) {
  await AsyncStorage.setItem(ONESIGNAL_STORAGE_USER, JSON.stringify(userId))
}

async function userNotificationGet() {
  const storage = await AsyncStorage.getItem(ONESIGNAL_STORAGE_USER);

  const notification = storage ? JSON.parse(storage) : {};

  return notification
}

async function userNotificationDelete() {
  await AsyncStorage.removeItem(ONESIGNAL_STORAGE_USER);
}

export const oneSignalStorage = {
  notification: {
    save: oneSignalStorageSave,
    get: oneSignalStorageGet,
    delete: oneSignalStorageDelete
  },

  user: {
    save: userNotificationSave,
    get: userNotificationGet,
    delete: userNotificationDelete
  }
};