import { useAuth } from '@contexts/auth';
import { Center, VStack, Text, Button, Icon, useToast, KeyboardAvoidingView } from 'native-base';


import Logo from '@assets/logo_longevisar_slogan.svg';
import { Input } from '@components/Input';
import { Ionicons } from '@expo/vector-icons';
import { Link } from 'expo-router';
import { Platform, TouchableOpacity } from 'react-native';
import { SubmitHandler, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from '@tanstack/react-query';
import { AxiosError, AxiosResponse } from 'axios';
import { api } from '@services/api';

type FormProps = {
	email: string;
};

const signUpResolver = yup.object().shape({
	email: yup.string()
		.required('Email obrigatório')
		.email('Digite um e-mail válido'),

})

export default function ForgotPassword() {
	const { signIn } = useAuth();
	const toast = useToast()

	const { handleSubmit, control, formState } = useForm<FormProps>({
		resolver: yupResolver(signUpResolver)
	})
	const { errors } = formState

	const forgotPassword = useMutation(async (values: FormProps) => {
		return await api.post('/v1/recovery_password/', {
			email: values.email,
			redirectUrl: 'reset_password'
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			toast.show({ description: 'E-mail de recuperação enviado', placement: 'top' });
		},
		onError: (error: AxiosError<any>) => {
			toast.show({ description: error.message || 'Ocorreu um erro ao recuperar a senha.', placement: 'top' });
		}
	})

	const handleForgotPassword: SubmitHandler<FormProps> = async (values) => {
		try {
			await forgotPassword.mutateAsync(values)
		} catch { }
	}

	return (
		<KeyboardAvoidingView
			behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
			flex={1}
		>
			<Center flex={1} p={8}>
				<VStack space={6} alignItems="center" w="100%">
					<Logo />
					<VStack space={4} w="100%">
						<Text
							fontSize="2xl"
							textAlign="center"
							fontWeight="light"
							color="#204194"
						>
							Recupere sua senha
						</Text>
						<Text
							fontSize="sm"

						>
							Preencha abaixo seu endereço de email para receber as instruções necessárias e criar uma nova senha!
						</Text>
						<Input
							name="email"
							placeholder="Digite seu email"
							error={errors.email}
							control={control}
							textContentType='emailAddress'
							keyboardType='email-address'
							autoCapitalize='none'
							autoCorrect={false}
							InputLeftElement={
								<Icon as={<Ionicons name={"mail-outline"} />}
									size={6}
									color={"#00436C"}
								/>
							}
						/>

						<Button
							onPress={handleSubmit(handleForgotPassword as any)}
							colorScheme="darkBlue"
						>
							ENVIAR
						</Button>
						<Link
							href="/sign-in"
							asChild
						>
							<TouchableOpacity>
								<Text
									color="darkBlue"
									textAlign="right"
								>
									Voltar
								</Text>
							</TouchableOpacity>
						</Link>

					</VStack>
				</VStack >
			</Center>
		</KeyboardAvoidingView>
	);
}
