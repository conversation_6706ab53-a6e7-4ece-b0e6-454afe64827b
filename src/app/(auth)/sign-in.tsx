import { useAuth } from '@contexts/auth';
import { Center, VStack, Text, Button, Icon, useToast, NativeBaseProvider, KeyboardAvoidingView } from 'native-base';
import * as yup from 'yup';
import { InferType } from 'yup';
import { yupResolver } from "@hookform/resolvers/yup";
import { Input } from '@components/Input';
import { Ionicons } from '@expo/vector-icons';
import { Link } from 'expo-router';
import { Platform, Pressable } from 'react-native';
import { useForm } from 'react-hook-form';

import Logo from '@assets/logo_longevisar_slogan.svg';

// type FormProps = {
// 	email: string;
// 	password: string;
// };

const signUpResolver = yup.object({
	email: yup.string()
		.required('Email obrigatório')
		.email('Digite um e-mail válido'),
	password: yup.string().required('Senha obrigatória'),
})

type FormProps = InferType<typeof signUpResolver>;

export default function SignIn() {
	const { signIn } = useAuth();
	const toast = useToast()


	const { handleSubmit, control, formState } = useForm<FormProps>({
		resolver: yupResolver(signUpResolver)
	})

	const { isSubmitting, errors } = formState


	function handleSignIn({ email, password }: FormProps) {
		signIn(email, password, toast);
	}

	return (
		<KeyboardAvoidingView
			behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
			flex={1}
		>
			<Center flex={1} p={8}>
				<VStack space={6} w="100%" alignItems="center">
					<Logo />
					<VStack space={4} w="100%">
						<Text
							fontSize="2xl"
							textAlign="center"
							fontWeight="light"
							color="#fff"
						>
							Seja bem-vindo
						</Text>
						<Input
							name="email"
							placeholder="Digite seu email"
							error={errors.email}
							control={control}
							autoCorrect={false}
							autoCapitalize="none"
							InputLeftElement={
								<Icon as={<Ionicons name={"mail-outline"} />}
									size={6}
									color={"#00436C"}
								/>
							}
						/>
						<Input
							name="password"
							placeholder="Digite sua senha"
							error={errors.password}
							control={control}
							autoCorrect={false}
							autoCapitalize="none"
							password={true}
							InputLeftElement={
								<Icon as={<Ionicons name={"lock-closed-outline"} />}
									size={6}
									color={"#00436C"}
								/>
							}
						/>
						<Link
							href="/forgot-password"
							asChild
						>
							<Pressable>
								<Text
									color="#fff"
									textAlign="right"
								>
									recuperar a senha?
								</Text>
							</Pressable>
						</Link>
						<Button
							isLoading={isSubmitting}
							onPress={handleSubmit(handleSignIn as any)}
							colorScheme="darkBlue"
						>
							ENTRAR
						</Button>
					</VStack>
				</VStack >
			</Center>
		</KeyboardAvoidingView>
	);
}
