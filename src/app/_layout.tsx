
import { Slot } from 'expo-router';
import { Box } from 'native-base';
import { Provider } from '@contexts/index';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'react-native';
import { LogLevel, OneSignal } from 'react-native-onesignal';
import { oneSignalStorage } from '@storage/storageOneSignal';

import type { NotificationType } from 'src/@types/Notification';
import { useEffect } from 'react';
import { api } from '@services/api';

const oneSignalId = '************************************';

export default function Layout() {
  const handleNotifications = async (notification: NotificationType) => {
    const notifications = await oneSignalStorage.notification.get();

    let newNotifications = JSON.parse(notifications);
    newNotifications = newNotifications === null ? [] : newNotifications;
    newNotifications.unshift({
      title: notification.title ? notification.title : 'Longevisar',
      body: notification.body,
      date: new Date(),
    });

    await oneSignalStorage.notification.save(newNotifications);
  };

  const getOneSinalId = async () => {
    const userId = await OneSignal.User.pushSubscription.getIdAsync();
    await oneSignalStorage.user.save(userId);

    // TODO: Implementar a chamada para a API
    try {
      await api.put('/v1/students/onesignal', { userOneSignalKey: userId });
    } catch (error) { }
  };

  SplashScreen.preventAutoHideAsync();

  useEffect(() => {
    // Remove this method to stop OneSignal Debugging
    OneSignal.Debug.setLogLevel(LogLevel.Verbose);

    // OneSignal Initialization
    OneSignal.initialize(oneSignalId);

    // requestPermission will show the native iOS or Android notification permission prompt.
    // We recommend removing the following code and instead using an In-App Message to prompt for notification permission
    OneSignal.Notifications.requestPermission(true);

    OneSignal.Notifications.addEventListener('click', (event) => {
      const notification = {
        title: 'Longevisar',
        body: event.notification.body,
        date: new Date(),
      }

      handleNotifications(notification);

    });
  }, []);

  useEffect(() => {
    getOneSinalId();
  }, [])

  return (
    <Provider>
      <Box
        position="absolute"
        w="100%"
        h="100%"
        backgroundColor={"#00436C"}
      />
      <Slot />
      <StatusBar barStyle='light-content' />
    </Provider>
  );
}
