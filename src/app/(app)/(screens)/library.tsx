import { useLibrary } from "@hooks/useLibrary";
import { Box, FlatList, Flex, HStack, Icon, Image, Pressable, Text, VStack } from "native-base";
import { Linking } from "react-native";
import { AntDesign, Entypo } from "@expo/vector-icons";
import { useState, Fragment } from "react";

export default function Library() {
	const [folderId, setFolderId] = useState(null)
	const [foldersName, setFoldersName] = useState([{ name: 'Inicio', folderId: null }])
	const { data: dataLibrary, isLoading } = useLibrary({
		folderId
	})

	const folders = dataLibrary?.folders || []
	const contents = dataLibrary?.contents || []

	const data = [...folders, ...contents]

	const handleFolder = (folder) => {
		setFolderId(folder.secure_id)
		setFoldersName([...foldersName, { name: folder?.name, folderId: folder.secure_id }])
	}

	const handleBackFolder = (item) => {
		const index = foldersName.indexOf(item)
		const foldersNames = foldersName.slice(0, index + 1)
		setFoldersName(foldersNames)
		setFolderId(item.folderId)
	}

	const handleDownload = (url) => {
		Linking.openURL(url)
	}

	return (<>
		{isLoading ? <Box />
			:
			<Box flex={1}>
				<HStack px={2} pt={2} space={1}>
					{foldersName.map((item, index) => (
						<Fragment key={item.folderId}>
							{index !== 0 ? <Text>/</Text> : null}
							<Pressable
								onPress={() => handleBackFolder(item)}
							>
								<Text>{item.name}</Text>
							</Pressable>
						</Fragment>
					))}
				</HStack>
				<FlatList
					flex={1}
					contentContainerStyle={{ flex: 1 }}
					p={5}
					data={data.flatMap(obj => obj)}
					keyExtractor={(item) => item.secure_id}
					renderItem={({ item }) => (
						<Box my={2}>
							{item.type === 'folder'
								? <Pressable
									variant={"ghost"}
									onPress={() => handleFolder(item)}
								>

									<HStack
										w={'full'}
										bg={'teal.100'}
										key={item.secure_id}
										space={2}
										p={3}
										bgColor="gray.200" rounded="md" shadow={1}
									>
										<Icon as={<Entypo
											name={"folder"}
										/>}
											size={5}
											mr="2"
											color={"#00436C"} />
										<Text>{item.name}</Text>

									</HStack>
								</Pressable>
								: <Pressable
									variant={"ghost"}
									onPress={() => handleDownload(item.upload.url)}
								>
									<HStack
										w={'full'}
										bg={'teal.100'}
										key={item.secure_id}
										space={2}
										p={3}
										bgColor="gray.200" rounded="md" shadow={1}
									>

										<Image
											source={{ uri: item.thumb.url }}
											alt="Imagem de Fundo"
											resizeMode="contain"
											rounded="xl"
											w={16}
											h={16}
										/>
										<VStack flex={1} justifyContent="center">
											<Text fontSize={16}>{item.name}</Text>
											<Text fontSize={12}>{item.description}</Text>
										</VStack>
										<Flex
											justifyContent="center"
										>
											<Icon as={<AntDesign
												name={"clouddownload"}
											/>}
												size={6}
												mr="2"
												color={"#00436C"} />
										</Flex>
									</HStack>


								</Pressable>
							}
						</Box>
					)}
				/>
			</Box>
		}
	</>
	);
}
