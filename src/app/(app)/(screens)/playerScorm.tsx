import { api } from "@services/api";
import { queryClient } from "@services/queryClient";
import { useLocalSearchParams } from "expo-router";
import { Box, ScrollView, Text, VStack } from "native-base";
import { useEffect, useRef, useState } from "react";
import WebView from "react-native-webview";

export default function PlayerScorm() {
  const webViewRef = useRef<WebView>(null);
  const [minute, setMinute] = useState(0);
  const { title, excerpt, description, url, secure_id, statusContentFinished } =
    useLocalSearchParams();

  const statusContentFinishedBoolean = statusContentFinished === "true";

  useEffect(() => {
    const interval = setInterval(() => {
      setMinute((prevMinute) => prevMinute + 1);
    }, 60000); // Update every minute

    return () => {
      clearInterval(interval);
    };
  }, []);

  useEffect(() => {
    // Update status every 5 minutes or when component unmounts
    if (minute > 0 && minute % 5 === 0) {
      updateStatus();
    }
  }, [minute]);

  const updateStatus = async () => {
    try {
      await api.post(`v1/students/status-lesson-content`, {
        lessonContentSelected: secure_id,
        finished: statusContentFinishedBoolean ? true : false,
        currentTime: minute * 60, // Convert minutes to seconds
        percentFinished: 50, // You can implement progress tracking if needed
      });
      queryClient.invalidateQueries(["MyCourseContentStudent"]);
    } catch (error) {
      console.log("Error updating status:", error);
    }
  };

  const handleWebViewMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);

      // Handle SCORM completion messages
      if (data.type === "scorm_complete" || data.type === "scorm_passed") {
        updateCompletionStatus();
      }
    } catch (error) {
      console.log("Error parsing webview message:", error);
    }
  };

  const updateCompletionStatus = async () => {
    try {
      await api.post(`v1/students/status-lesson-content`, {
        lessonContentSelected: secure_id,
        finished: true,
        currentTime: minute * 60,
        percentFinished: 100,
      });
      queryClient.invalidateQueries(["MyCourseContentStudent"]);
    } catch (error) {
      console.log("Error updating completion status:", error);
    }
  };

  // Inject JavaScript to handle SCORM events
  const injectedJavaScript = `
    // Listen for SCORM events
    if (window.API_1484_11) {
      const originalSetValue = window.API_1484_11.SetValue;
      window.API_1484_11.SetValue = function(element, value) {
        if (element === 'cmi.completion_status' && value === 'completed') {
          window.ReactNativeWebView.postMessage(JSON.stringify({type: 'scorm_complete'}));
        }
        if (element === 'cmi.success_status' && value === 'passed') {
          window.ReactNativeWebView.postMessage(JSON.stringify({type: 'scorm_passed'}));
        }
        return originalSetValue.call(this, element, value);
      };
    }
    
    // Alternative: Listen for common SCORM completion events
    window.addEventListener('beforeunload', function() {
      window.ReactNativeWebView.postMessage(JSON.stringify({type: 'page_unload'}));
    });
    
    true; // Required for injected JavaScript
  `;

  return (
    <VStack flex={1}>
      <Box flex={1}>
        <WebView
          ref={webViewRef}
          style={{ flex: 1 }}
          source={{ uri: url as string }}
          onMessage={handleWebViewMessage}
          injectedJavaScript={injectedJavaScript}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          mixedContentMode="compatibility"
          allowsInlineMediaPlayback={true}
          mediaPlaybackRequiresUserAction={false}
        />
      </Box>

      {/* Content Information */}
      <ScrollView maxHeight={200}>
        <VStack space={1} p={4}>
          {title && (
            <Text fontSize="lg" fontWeight="semibold" color="#00436C">
              {title}
            </Text>
          )}
          {excerpt && (
            <Text fontSize="md" color="#00436C">
              {excerpt}
            </Text>
          )}
          {description && <Text color="#00436C">{description}</Text>}
        </VStack>
      </ScrollView>
    </VStack>
  );
}
