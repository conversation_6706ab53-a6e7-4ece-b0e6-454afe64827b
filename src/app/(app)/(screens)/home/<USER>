import { useHome } from "@hooks/useHome";
import { <PERSON><PERSON><PERSON>, H<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Spinner, Text, VStack, View } from "native-base";
import Categories from "./components/categories";


export default function Home() {
	const { data: dataHome, isLoading } = useHome()


	if (isLoading) {
		return (
			<HStack space={2} justifyContent="center" flex={1} alignItems="center">
				<Spinner color="#00436C" />
				<Heading color="#00436C" fontSize="sm">
					Carregando
				</Heading>
			</HStack>)
	}

	if (!dataHome) {
		return (
			<HStack space={2} justifyContent="center" flex={1} alignItems="center">
				<Heading color="#00436C" fontSize="sm">
					Não foi encontrado nenhum curso.
				</Heading>
			</HStack>)
	}

	return (
		<FlatList
			data={dataHome.categories}
			ItemSeparatorComponent={() => <View style={{ width: 20 }} />}
			keyExtractor={(item) => item.secure_id}
			renderItem={({ item }) => (<Categories key={item.secure_id} data={item} />)}
		/>
	);
}
