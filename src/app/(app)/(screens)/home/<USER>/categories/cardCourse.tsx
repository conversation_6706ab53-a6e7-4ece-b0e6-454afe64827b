import { Link } from "expo-router";
import { AspectRatio, Box, HStack, Image, Pressable, Text, VStack } from "native-base";
import { MyCourseStudent } from "src/@types/myCourses";


type CardCourseProps = {
	data: MyCourseStudent
}

export default function CardCourse({ data }: CardCourseProps) {

	return (
		<Link href={`/course?courseSecureId=${data.secure_id}&subscriptionSecureId=${data.subscription}`}>
			<Box
				w={40}
			>
				<VStack space={1}>
					<Box
						bgColor="white"
						shadow={2}
						rounded="xl"
					>
						<AspectRatio
							ratio={{
								base: 16 / 9
							}}
						>

							<Image
								source={{ uri: data.thumb.url }}
								alt="Imagem de Fundo"
								resizeMode="cover"
								rounded="xl"
							/>

						</AspectRatio>
					</Box>
					<VStack>
						<Text
							fontSize="xs"
							fontWeight="semibold"
							color="#00436C"
						>
							{data.title}
						</Text>
						<Text
							fontSize="xs"
							color="#00436C"
							noOfLines={2}
						>
							{data.excerpt}
						</Text>
					</VStack>
				</VStack>
			</Box>
		</Link>
	)

}
