import { Flat<PERSON><PERSON>, HStack, Text, VStack, View } from "native-base";
import CardCourse from "./cardCourse";
import { ListMyCoursesStudent } from "src/@types/myCourses";

type CategoriesProps = {
	data: ListMyCoursesStudent
}


export default function Categories({ data }: CategoriesProps) {
	const courses = data.courses

	return (
		<VStack space={2}>
			<Text
				fontWeight="bold"
				fontSize="md"
				color="#00436C"
				ml={5}

			>{data.title}</Text>
			<FlatList
				data={courses}
				horizontal
				keyExtractor={(item) => item.secure_id}
				ItemSeparatorComponent={() => <View style={{ width: 20 }} />}
				renderItem={({ item }) => (<CardCourse key={item.secure_id} data={item} />)}
				pl={5}
				pt={1}
			/>
		</VStack >
	)
}
