import { useCourseContent } from "@contexts/course"
import { api } from "@services/api"
import { useMutation, useQuery } from "@tanstack/react-query"
import { useLocalSearchParams } from "expo-router"
import { Button, Progress, Text, VStack, useToast } from "native-base"
import { useState } from "react"
import QuestionCard from "./components/questionCard"
import { QuestionSurveyProps, SurveyProps } from "src/@types/researches"
import { AxiosError, AxiosResponse } from "axios"
import { queryClient } from "@services/queryClient"
import { useRouter } from "expo-router"
import Loading from "@components/loading"

export default function ResponseResearch() {
	const toast = useToast()
	const router = useRouter()
	const [started, setStarted] = useState(false)
	const [questionSecureId, setSetQuestionSecureId] = useState<string>(null)
	const { course } = useCourseContent()
	const subscriptionSecureId = course.subscription
	const { researchSecureId } = useLocalSearchParams<{ researchSecureId: string }>()

	const { data: dataSurvey } = useQuery(['responseResearch', researchSecureId], async () => {
		const commentsResponse = await api.get(`/v1/powerups/search-management/survey/logged/${researchSecureId}`, {
			params: {
				subscriptionSecureId,
				type: 'lesson',
			}
		})
		return commentsResponse.data as SurveyProps
	})

	const { data: dataQuestion } = useQuery(['questionResearch', questionSecureId], async () => {
		if (!questionSecureId) return null

		const questionResponse = await api.get(`/v1/powerups/search-management/survey/logged-question/${questionSecureId}`, {
			params: {
				leadSearchSecureId: dataSurvey.leadSearchSecureId,
				subscriptionSecureId,
			}
		})
		return questionResponse.data as QuestionSurveyProps
	})

	const handleStarded = async () => {
		setSetQuestionSecureId(dataSurvey.survey.firstQuestion)
		setStarted(true)
	}

	const responseResearch = useMutation(async (values) => {
		return await api.put(`v1/powerups/search-management/survey/logged/${dataSurvey.leadSearchSecureId}`, {
			choiceSecureId: dataQuestion.currentQuestion.type === "only_choice" ? [values] : dataQuestion.currentQuestion.type === "multiple_choices" ? values : undefined,
			scale: dataQuestion.currentQuestion.type === 'selection_scale' ? values : undefined,
			response: dataQuestion.currentQuestion.type === 'essay' ? values : undefined,
			finished: !dataQuestion.nextQuestionSecureId,
			questionSecureId,
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['responseResearch'])
			queryClient.invalidateQueries(['questionResearch'])
			if (dataQuestion.nextQuestionSecureId) {
				setSetQuestionSecureId(dataQuestion.nextQuestionSecureId)
			} else {
				toast.show({
					description: 'Pesquisa finalizada!',
					placement: "top",
				})
				queryClient.invalidateQueries(['Researches'])
				router.back()
			}
		},
		onError: (error: AxiosError<any>) => {
			toast.show({
				description: error.response ? error.response.data.message : 'Erro ao responder atividade!',
				placement: "top",
			})
		}
	})

	const handleResponse = async (values) => {
		try {
			if (!dataSurvey.finished) {
				await responseResearch.mutateAsync(values)
			} else {
				if (dataQuestion.nextQuestionSecureId) {
					setSetQuestionSecureId(dataQuestion.nextQuestionSecureId)
				} else {
					router.back()
				}
			}
		} catch { }
	}

	if (!dataSurvey) {
		return <Loading />
	}
	const percent = !!dataQuestion ? ((dataQuestion.currentQuestionIndex + 1) / dataQuestion.total) * 100 : 0
	return (
		<VStack space={4} p={4}>
			<Text fontSize="md" fontWeight="semibold" textAlign="center" color="darkBlue.800">{dataSurvey.survey.title}</Text>
			{!started && <>
				<Text fontSize="xs" color="darkBlue.800">{dataSurvey.survey.description}</Text>
				<Button
					colorScheme="darkBlue"
					onPress={handleStarded}
				>
					{dataSurvey.finished ? 'Ver respostas' : 'Iniciar'}
				</Button></>}
			{started && !!dataQuestion && (
				<VStack space={4}>
					<Progress colorScheme="darkBlue" value={percent} mx="4" />
					<QuestionCard data={dataQuestion} handleResponse={handleResponse} handleQuestion={(secureId) => setSetQuestionSecureId(secureId)} />
				</VStack>
			)}
		</VStack>
	)
}
