import { MaterialCommunityIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import { Box, Button, HStack, Icon, Text } from "native-base";
import { ResearchProps } from "src/@types/researches";

type ResearchCardProps = {
	data: ResearchProps;
}

export default function ResearchCard({ data }: ResearchCardProps) {

	const handleResearch = () => {
		router.push({
			pathname: `/course/lessons/research/responseResearch`,
			params: { researchSecureId: data.secure_id }
		})
	}

	return (
		<Box
			bgColor="blueGray.200"
			p={4}
			rounded={10}
		>
			<HStack space={2} alignItems="center">
				<Text
					flex={1}
					fontSize="xs"
				>
					{data.search.title}
				</Text>
				<Button
					leftIcon={<Icon as={MaterialCommunityIcons} name="message-question-outline" />}
					variant="ghost"
					colorScheme="darkBlue"
					size="sm"
					onPress={handleResearch}
				>
					{data.leadSearches.length && data.leadSearches[0].finished ? 'Ver Pesquisa' : 'Responder'}
				</Button>
			</HStack>
		</Box>
	)
}
