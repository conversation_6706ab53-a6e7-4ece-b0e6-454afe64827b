import { AntDesign } from "@expo/vector-icons";
import { Box, Button, Checkbox, Flex, HStack, Icon, Radio, Text, TextArea, VStack } from "native-base";
import { useEffect, useState } from "react";
import { QuestionSurveyProps } from "src/@types/researches";

type QuestionCardProps = {
	data: QuestionSurveyProps;
	handleResponse: any
	handleQuestion: any
}

export default function QuestionCard({ data, handleResponse, handleQuestion }: QuestionCardProps) {
	const [value, setValue] = useState(null)

	useEffect(() => {
		if (!!data && data.responses && data.responses.length) {
			data.currentQuestion.type === 'selection_scale' && setValue(data.responses[0]?.scale.toString())
			data.currentQuestion.type === 'only_choice' && setValue(data.responses[0]?.choice?.secure_id)
			data.currentQuestion.type === 'multiple_choices' && setValue(data.responses.map(value => value.choice.secure_id))
			data.currentQuestion.type === 'essay' && setValue(data.responses[0]?.response)

		} else {
			setValue(null)
		}
	}, [data])

	return (
		<Box
			bgColor="blueGray.200"
			p={4}
			rounded={10}
		>
			<VStack space={4}>
				<Text
					fontWeight="semibold"
				>
					{data.currentQuestion.content}
				</Text>
				{data.currentQuestion.type === 'selection_scale' && (
					<Radio.Group
						name="responses"
						value={value}
						defaultValue={value}
						onChange={nextValue => {
							setValue(nextValue);
						}}
						isDisabled={!!data.finished}
					>

						<Flex direction="row" flexWrap="wrap" justifyContent="space-between" width={"100%"}>
							<Box flex={1}>
								<Radio key={1} value={'1'} my={1} size="sm" colorScheme="darkBlue" isDisabled={!!data.finished} >
									{1}
								</Radio>
							</Box>
							<Box flex={1}>
								<Radio key={2} value={'2'} my={1} size="sm" colorScheme="darkBlue" isDisabled={!!data.finished}>
									{2}
								</Radio>
							</Box>
							<Box flex={1}>
								<Radio key={3} value={'3'} my={1} size="sm" colorScheme="darkBlue" isDisabled={!!data.finished}>
									{3}
								</Radio>
							</Box>
							<Box flex={1}>
								<Radio key={4} value={'4'} my={1} size="sm" colorScheme="darkBlue" isDisabled={!!data.finished}>
									{4}
								</Radio>
							</Box>
							<Box flex={1}>
								<Radio key={5} value={'5'} my={1} size="sm" colorScheme="darkBlue" isDisabled={!!data.finished}>
									{5}
								</Radio>
							</Box>
						</Flex>
						<Flex direction="row" flexWrap="wrap" justifyContent="space-between" width={"100%"} marginTop={5}>
							<Box flex={1}>
								<Radio key={6} value={'6'} my={1} size="sm" colorScheme="darkBlue" isDisabled={!!data.finished}>
									{6}
								</Radio>
							</Box>
							<Box flex={1}>
								<Radio key={7} value={'7'} my={1} size="sm" colorScheme="darkBlue" isDisabled={!!data.finished}>
									{7}
								</Radio>
							</Box>
							<Box flex={1}>
								<Radio key={8} value={'8'} my={1} size="sm" colorScheme="darkBlue" isDisabled={!!data.finished}>
									{8}
								</Radio>
							</Box>
							<Box flex={1}>
								<Radio key={9} value={'9'} my={1} size="sm" colorScheme="darkBlue" isDisabled={!!data.finished}>
									{9}
								</Radio>
							</Box>
							<Box flex={1}>
								<Radio key={10} value={'10'} my={1} size="sm" colorScheme="darkBlue" isDisabled={!!data.finished}>
									{10}
								</Radio>
							</Box>
						</Flex>
					</Radio.Group>
				)}
				{data.currentQuestion.type === 'only_choice' && (
					<Radio.Group
						name="responses"
						value={value}
						onChange={nextValue => {
							setValue(nextValue);
						}}
						isDisabled={!!data.finished}
					>
						<VStack>
							{data.currentQuestion.choices.map(item => (
								<Radio key={item.secure_id} value={item.secure_id} my={1} size="sm" colorScheme="darkBlue" isDisabled={!!data.finished}>
									{item.content}
								</Radio>))}
						</VStack>

					</Radio.Group>
				)}
				{data.currentQuestion.type === 'multiple_choices' && (
					<Checkbox.Group
						value={value && Array.isArray(value) ? value : []}
						onChange={nextValue => {
							setValue(nextValue);
						}}
					>
						{data.currentQuestion.choices.map(item => (
							<Checkbox key={item.secure_id} value={item.secure_id} my={1} size="sm" colorScheme="darkBlue" isDisabled={!!data.finished}>
								{item.content}
							</Checkbox>))}
					</Checkbox.Group>
				)}
				{data.currentQuestion.type === 'essay' && (
					<TextArea
						h={20}
						placeholder="Digite aqui sua resposta"
						w="100%"
						onChangeText={value => setValue(value)}
						defaultValue={value}
						isDisabled={!!data.finished}
						tvParallaxProperties={undefined}
						onTextInput={undefined}
						autoCompleteType={undefined}
					/>
				)}
				<HStack space={2}>
					{!!data.previousQuestionSecureId && <Button
						flex={1}
						colorScheme="darkBlue"
						variant={'outline'}
						leftIcon={<Icon as={AntDesign} name="left" />}
						onPress={() => handleQuestion(data.previousQuestionSecureId)}
					>
						Voltar
					</Button>}
					<Button
						flex={1}
						colorScheme="darkBlue"
						onPress={() => handleResponse(value)}
						rightIcon={!!data.nextQuestionSecureId ? <Icon as={AntDesign} name="right" /> : undefined}
						isDisabled={!value}
					>
						{!!data.nextQuestionSecureId ? 'Proximo' : !!data.finished ? 'Fechar' : 'Finalizar'}
					</Button>

				</HStack>
			</VStack>
		</Box >
	)
}
