import { Center, FlatList, Flex, Text, VStack, View } from "native-base"
import ResearchCard from "./components/researchCard"
import { useResearches } from "@hooks/useResearches"
import { useCourseContent } from "@contexts/course"
import { useLocalSearchParams } from "expo-router"

import SurveySVG from '@assets/survey.svg';

export default function Researches() {
	const { course } = useCourseContent()
	const { lessonSecureId } = useLocalSearchParams<{ lessonSecureId: string }>()
	const subscriptionSecureId = course.subscription

	const { data } = useResearches({ subscriptionSecureId, lessonSecureId })
	if (data && data.length <= 0) {
		return <Center flex={1}>
			<SurveySVG />
			<Text fontSize="xl" color="darkBlue.600">Pesquisas não encontradas</Text>
			<Text fontSize="md" color="blueGray.500">No momento não tem pesquisa disponível!</Text>
		</Center>
	}
	return (
		<FlatList
			ListHeaderComponent={<Text textAlign="center" fontSize="md" fontWeight="semibold" mb={2} color="darkBlue.800">Pesquisas</Text>}
			data={data}
			keyExtractor={(item) => item.secure_id}
			renderItem={({ item }) => <ResearchCard key={item.secure_id} data={item} />}
			ItemSeparatorComponent={() => <View h={5} />}
			showsHorizontalScrollIndicator={false}
			p={4}
		/>
	)
}
