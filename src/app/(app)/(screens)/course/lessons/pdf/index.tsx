
import { Box, Image, FlatList, Button, Icon } from "native-base"
import { api } from "@services/api"
import { queryClient } from "@services/queryClient"
import { AntDesign } from "@expo/vector-icons"
import { router, useLocalSearchParams } from "expo-router"
import { useCourseContent } from "@contexts/course"

export default function PdfsList() {
	const { course } = useCourseContent()
	const { lessonSecureId } = useLocalSearchParams()

	const content = course.lessons.find(item => item.secure_id === lessonSecureId)

	const handleOpenPdf = async (pdfData) => {
		await api.post(`v1/students/status-lesson-content`, {
			lessonContentSelected: pdfData.secure_id,
			finished: true
		})
		queryClient.invalidateQueries(['MyCourseContentStudent'])

		// Linking.openURL(Platform.OS === 'ios' ? pdfData.upload.url : `https://docs.google.com/viewerng/viewer?url=${pdfData.upload.url}`)
		router.push({
			pathname: '/pdfView',
			// params: { url: `https://docs.google.com/viewerng/viewer?url=${pdfData.upload.url}` }

			params: { url: `${pdfData.upload.url}` }
		})
	}

	return (
		<FlatList
			data={content.pdfs}
			numColumns={3}
			padding={5}
			keyExtractor={(item) => item.secure_id}
			// ItemSeparatorComponent={() => <View h={5} />}
			renderItem={({ item }) => (
				<Button
					variant={"ghost"}
					onPress={() => handleOpenPdf(item)}
				>
					<Box
						key={item.secure_id}
						w={24}
						h={32}
					>
						<Image
							source={{ uri: item.thumb.url }}
							alt="Imagem de Fundo"
							resizeMode="cover"
							rounded="xl"
							w="100%"
							h="100%"
						/>
						{item.userStatusContent && item.userStatusContent.finished &&
							<Box position="absolute" right={2} top={2}>
								<Icon as={AntDesign} name="checkcircle" color="#00436C" size="lg" />
							</Box>
						}
					</Box>
				</Button>
			)}

		/>
	)

}
