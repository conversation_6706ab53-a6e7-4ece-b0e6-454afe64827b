import { useLessonActivities } from "@hooks/useLessonActivities"
import { useLocalSearchParams } from "expo-router"
import { FlatList, KeyboardAvoidingView, Spinner, Text, View } from "native-base"
import ActivityEssayCard from "./components/activityEssayCard"
import { useCourseContent } from "@contexts/course"
import { Platform } from "react-native"

type CourseProps = {
	lessonSecureId: string
	subscriptionSecureId: string
	search?: string;
}

export default function ActivitiesList() {
	const { lessonSecureId, search } = useLocalSearchParams<CourseProps>()
	const { course } = useCourseContent()
	const { data: dataActivities, hasNextPage, fetchNextPage, isLoading, isFetchingNextPage } = useLessonActivities({
		lessonSecureId,
		search,
		limit: 5,
		subscriptionSecureId: course.subscription,
	})

	const activityType = (item) => {
		switch (item.type) {
			case 'essay':
				return <ActivityEssayCard key={item.secure_id} data={item} />
			case 'choice':
				break;
			case 'selection_box':
				break;
			case 'selection_scale':
				break;
			default:
				break;
		}
	}

	return <KeyboardAvoidingView
		behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
		flex={1}
	>
		{dataActivities && dataActivities.pages[0].activities.length
			? (

				<FlatList
					ListHeaderComponent={<Text textAlign="center" fontSize="md" fontWeight="medium" color="#00436C" mb={4}>Atividades</Text>}
					p={5}
					data={!isFetchingNextPage ? dataActivities.pages.flatMap(obj => obj.activities) : []}
					keyExtractor={(item) => item.secure_id}
					showsHorizontalScrollIndicator={false}
					ItemSeparatorComponent={() => <View h={5} />}
					ListFooterComponent={isFetchingNextPage ? <Spinner color="indigo.500" /> : undefined}
					progressViewOffset={6}
					onScrollEndDrag={() => {
						if (hasNextPage) {
							fetchNextPage()
						}
					}}
					contentContainerStyle={{ paddingBottom: 60 }}
					renderItem={({ item }) => activityType(item)}
				/>)
			: isLoading ? <Spinner color="indigo.500" /> : <Text>Sem atividades</Text>
		}
	</KeyboardAvoidingView>
}
