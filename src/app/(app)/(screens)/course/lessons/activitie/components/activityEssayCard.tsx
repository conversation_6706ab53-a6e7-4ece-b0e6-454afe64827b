import { InputTextArea } from "@components/InputTextArea";
import { Box, Button, Center, Container, Divider, Flex, HStack, Image, Text, VStack, useDisclose, useToast } from "native-base";
import * as yup from 'yup';
import { InferType } from 'yup';
import { yupResolver } from "@hookform/resolvers/yup";
import { SubmitHandler, useForm } from "react-hook-form";
import { useEffect, useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { api } from "@services/api";
import { queryClient } from "@services/queryClient";
import { AxiosError, AxiosResponse } from "axios";
import { useCourseContent } from "@contexts/course";
import CommentCard from "./commentCard";

// type FormProps = {
// 	responseUser: string;
// };

type CommentProps = {
	secure_id: string;
	comment: string;
	user: {
		secure_id: string;
		userInfo: {
			name: string;
		};
	};
}

const formResolver = yup.object().shape({
	responseUser: yup.string().required('Resposta é obrigatório')
})

type FormProps = InferType<typeof formResolver>;

export default function ActivityCard({ data }) {
	const [isAnswered, setIsAnswered] = useState(!!data.userResponse)
	const toast = useToast()
	const {
		isOpen,
		onToggle
	} = useDisclose();
	const { course } = useCourseContent()

	const { handleSubmit, control, formState, setValue } = useForm<FormProps>({
		resolver: yupResolver(formResolver)
	})
	const { isSubmitting, errors } = formState

	const responseQuestion = useMutation(async (values: FormData) => {
		return await api.post('v1/students/response-activity', {
			...values,
			activitySecureId: data.secure_id,
			classeSecureId: course.classeSecureId,
			planSecureId: course.planSecureId,
			typeActivity: data.type
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['MyCourseContentStudent'])
			if (data.certificateBadge) {
				toast.show({
					render: () => {
						return <Box bg="#00436C" px="2" py="1" rounded="md" mb={5}>
							<Text color={'white'}>
								{data.certificateBadge.description}
							</Text>
							<Center m={4}>
								<Image
									source={{ uri: data.certificateBadge.badge.url }}
									alt="Imagem de Fundo"
									w={10}
									h={10}
								/>
							</Center>
						</Box>;
					},
					placement: "top",
				},
				)

			} else {
				toast.show({
					description: response.data.message ? response.data.message : 'Atividade respondida!',
					placement: "top",
				})
			}
			setIsAnswered(true)
		},
		onError: (error: AxiosError<any>) => {
			toast.show({
				description: error.response ? error.response.data.message : 'Erro ao responder atividade!',
				placement: "top",
			})
		}
	})

	const handleNewResponse: SubmitHandler<FormData> = async (values) => {
		try {
			await responseQuestion.mutateAsync(values)
		} catch { }
	}

	const { data: comments } = useQuery(['comments', data?.userResponse?.secure_id], async () => {
		if (data?.userResponse?.secure_id) {
			const commentsResponse = await api.get(`/v1/students/lesson-activity-comment/${data.userResponse.secure_id}`)
			return commentsResponse.data as CommentProps[]
		}
		return []
	})

	useEffect(() => {
		if (data.userResponse) {
			setValue('responseUser', data.userResponse.response)
		}
	}, [data])

	return (
		<Box
			bgColor="blueGray.200"
			p={4}
			rounded="md"
			shadow={1}
		>
			<VStack space={2}>
				<Text
					color="#00436C"
					fontSize="sm"
				>
					{data.question}
				</Text>
				<Divider />
				<VStack space={3}>
					<Text
						color="#00436C"
						fontSize="md"
					>
						Resposta
					</Text>
					<InputTextArea
						name="responseUser"
						placeholder="Digite sua resposta"
						error={errors.responseUser}
						control={control}
						numberOfLines={6}
						h={32}
						isDisabled={isAnswered}
					/>
					{!isAnswered && <Button
						colorScheme="darkBlue"
						onPress={handleSubmit(handleNewResponse as any)}
						isLoading={isSubmitting}
					>
						Responder
					</Button>}
					{isAnswered && comments && comments.length > 0 && !isOpen && <Button
						colorScheme="darkBlue"
						variant="outline"
						onPress={onToggle}
						isLoading={isSubmitting}
					>
						Mostrar Comentários
					</Button>}
					{!!isOpen && <Button
						size="xs"
						colorScheme="darkBlue"
						variant="ghost"
						onPress={onToggle}
					>
						<Text fontSize="xs">
							Ocultar Comentários
						</Text>
					</Button>}
					{isOpen && data.userResponse.secure_id && (
						<CommentCard lessonActivityResSecureId={data.userResponse?.secure_id} comments={comments} status={data.userResponse?.status} />
					)}
				</VStack>

			</VStack>

		</Box>
	)
}
