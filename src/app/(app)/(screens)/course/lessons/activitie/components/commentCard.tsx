import { <PERSON>, <PERSON><PERSON>, Text, VStack, useToast } from "native-base";
import CommentItemCard from "./commentItemCard";
import { InputTextArea } from "@components/InputTextArea";
import { SubmitHandler, useForm } from "react-hook-form";
import * as yup from 'yup';
import { InferType } from 'yup';
import { yupResolver } from "@hookform/resolvers/yup";
import { AxiosError, AxiosResponse } from "axios";
import { useMutation } from "@tanstack/react-query";
import { api } from "@services/api";
import { queryClient } from "@services/queryClient";
import { useRef } from "react";

type CommentProps = {
	lessonActivityResSecureId: string;
	comments: {
		secure_id: string;
		comment: string;
		user: {
			secure_id: string;
			userInfo: {
				name: string;
			};
		};
	}[];
	status: string;
}

// type FormProps = {
// 	comment: string;
// };

const formResolver = yup.object({
	comment: yup.string().required('Comentário não pode ser em vazio')
})

type FormProps = InferType<typeof formResolver>;

export default function CommentCard({ lessonActivityResSecureId, comments, status }: CommentProps) {
	const toast = useToast()
	const { handleSubmit, control, formState, reset } = useForm<FormProps>({
		resolver: yupResolver(formResolver)
	})
	const { isSubmitting, errors } = formState

	const commentResponse = useMutation(async (values: FormProps) => {
		return await api.post('v1/students/lesson-activity-comment', {
			...values,
			lessonActivityResSecureId
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			reset()
			toast.show({
				description: response.data.message ? response.data.message : 'Comentário enviado!',
				placement: "top",
			})
			queryClient.invalidateQueries(['comments'])

		},
		onError: (error: AxiosError<any>) => {
			toast.show({
				description: error.response ? error.response.data.message : 'Erro ao responder atividade!',
				placement: "top",
			})
		}
	})

	const handleNewComment: SubmitHandler<FormProps> = async (values) => {
		try {
			await commentResponse.mutateAsync(values)
		} catch { }
	}

	return (
		<Box
			bgColor="blueGray.100"
			p={4}
			rounded={8}
		>
			<VStack space={4}>
				<Text
					color="darkBlue.700"
					fontSize="md"
					textAlign="center"
				>
					Comentários
				</Text>

				{comments.map(item => <CommentItemCard key={item.secure_id} data={item} />)}
			</VStack>
			{status !== 'finished' && <VStack mt={5} space={4}>
				<InputTextArea name="comment" control={control} />
				<Button
					colorScheme="darkBlue"
					onPress={handleSubmit(handleNewComment)}
					isLoading={isSubmitting}
				>
					Enviar
				</Button>
			</VStack>}
		</Box>
	)
}
