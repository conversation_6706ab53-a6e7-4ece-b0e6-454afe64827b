import { useAuth } from "@contexts/auth";
import { Box, Text, VStack } from "native-base";

type CommentItemCardProps = {
	data: {
		secure_id: string;
		comment: string;
		user: {
			secure_id: string;
			userInfo: {
				name: string;
			};
		};
	}
}

export default function CommentItemCard({ data }: CommentItemCardProps) {
	const { user } = useAuth()
	const type = user.secureId === data.user.secure_id ? 'student' : 'tutor'

	switch (type) {
		case 'tutor':
			return (
				<VStack>
					<Box
						bgColor="blueGray.200"
						p={4}
						rounded={8}
						roundedBottomRight={0}
					>
						<Text fontSize="xs" color="darkBlue.800">{data.comment}</Text>
					</Box>
					<Text
						textAlign="right"
						fontSize="2xs"
						color="blueGray.600"
					>
						{data.user.userInfo.name}
					</Text>
				</VStack>
			);

		default:
			return (
				<VStack>
					<Box
						bgColor="blueGray.300"
						p={4}
						rounded={8}
						roundedBottomLeft={0}
					>
						<Text fontSize="xs" color="darkBlue.800">{data.comment}</Text>
					</Box>
					<Text textAlign="left" fontSize="2xs" color="blueGray.600">{data.user.userInfo.name}</Text>
				</VStack>

			);
	}
}
