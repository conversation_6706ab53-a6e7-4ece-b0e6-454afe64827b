import { getDateDifference } from "@utils/FormatDates";
import { Box, Divider, Text, VStack } from "native-base";
import { memo } from "react";
import RenderHTML, { HTMLSource } from "react-native-render-html";
import { ListQuestionAndAnswerProps } from "src/@types/questionAndAnswer";

type QuestionProps = {
	data: ListQuestionAndAnswerProps;
}

type HtmlDisplayProps = {
	html: string
}

export default function Question({ data }: QuestionProps) {

	const HtmlDisplay = memo(function HtmlDisplay({ html }: HtmlDisplayProps) {
		return (
			<RenderHTML
				baseStyle={{ color: "#1e3a8a" }}
				contentWidth={100}
				source={{ html }}
			/>
		)
	})

	return (
		<Box
			bgColor="blueGray.200"
			p={4}
			rounded="md"
			shadow={1}
		>
			<VStack>
				<VStack>
					<Text
						color="#00436C"
						fontSize="xs"
					>
						{data.user.userInfo.name}
					</Text>
					<Text
						color="#00436C"
						fontSize="2xs"
					>
						{getDateDifference(data.created_at)}
					</Text>
					<Text
						color="blue.800"
						fontSize="lg"
						fontWeight="semibold"
					>
						{data.title}
					</Text>
					<Text
						color="#00436C"
						fontSize="md"
					>
						{data.question}
					</Text>
				</VStack>
				{data.responses.length && data.responses.map(item =>
					<VStack key={item.secure_id}>
						<Divider
							my="2"
							bg="gray.300"
						/>
						<Text
							color="#00436C"
							fontSize="xs"
						>
							{item.user.userInfo.name}
						</Text>
						<Text
							color="#00436C"
							fontSize="2xs"
						>
							{getDateDifference(item.created_at)}
						</Text>
						<HtmlDisplay
							html={item.response}
						/>
					</VStack>
				)}
			</VStack>
		</Box>
	)

}
