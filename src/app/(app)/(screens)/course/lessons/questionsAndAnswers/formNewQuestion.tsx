import { Input } from "@components/Input";
import { Button, KeyboardAvoidingView, Text, VStack, useToast } from "native-base";
import { useForm } from "react-hook-form";
import * as yup from 'yup';
import { InferType } from 'yup';
import { yupResolver } from "@hookform/resolvers/yup";
import { InputTextArea } from "@components/InputTextArea";
import { api } from "@services/api";
import { Platform } from "react-native";
import { useCourseContent } from "@contexts/course"

// type FormProps = {
// 	title: string;
// 	question: string;
// };

const formResolver = yup.object().shape({
	title: yup.string().required('O título é obrigatório'),
	question: yup.string().required('O título é obrigatório')
})

type FormProps = InferType<typeof formResolver>;

export default function FormNewQuestion({ formAddRef, lessonSecureId }) {
	const toast = useToast()
	const { handleSubmit, control, formState, reset } = useForm<FormProps>({
		resolver: yupResolver(formResolver)
	})
	const { isSubmitting, errors } = formState
	const { course } = useCourseContent()
	const { classeSecureId, secure_id: courseSecureId } = course

	const handleNewQuestion = async (data) => {
		const newQuestion = {
			...data,
			courseId: courseSecureId,
			classeId: classeSecureId,
			lessonId: lessonSecureId
		}

		try {
			await api.post('/v1/students/question-and-answer', newQuestion)
			toast.show({ description: 'Pergunta enviada com sucesso!', placement: 'top' });
			reset()
			formAddRef.current?.close();
		} catch (error) {

			toast.show({ description: 'Ocorreu um erro ao enviar a pergunta!', placement: 'top' });
		}

	}

	return (
		<KeyboardAvoidingView
			behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
			flex={1}
		>
			<VStack space={5} p={5} pb={8}>
				<Text
					fontSize="xl"
					color="#00436C"
				>
					Pergunta
				</Text>
				<Input
					label="Título"
					name="title"
					placeholder="Digite o título da sua pergunta"
					error={errors.title}
					control={control}
				/>
				<InputTextArea
					label="Pergunta"
					name="question"
					placeholder="Digite sua pergunta"
					error={errors.question}
					control={control}
					numberOfLines={6}
					h={32}
				/>
				<Button
					colorScheme="darkBlue"
					onPress={handleSubmit(handleNewQuestion as any)}
					isLoading={isSubmitting}
				>
					Publicar Pergunta
				</Button>
			</VStack>
		</KeyboardAvoidingView>
	)
}
