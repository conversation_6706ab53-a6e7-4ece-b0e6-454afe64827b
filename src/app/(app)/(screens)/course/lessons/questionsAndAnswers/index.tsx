import { useQuestionAndAnswer } from "@hooks/useQuestionAndAnswer";
import { Center, Checkbox, FlatList, HStack, Icon, IconButton, Spinner, Text, VStack, View } from "native-base";
import Question from "./question";
import { useRef, useState } from "react";
import { Entypo } from "@expo/vector-icons";
import { Modalize } from "react-native-modalize";
import { Portal } from "react-native-portalize";
import FormNewQuestion from "./formNewQuestion";
import { useLocalSearchParams } from "expo-router";


import QuestionSVG from '@assets/question.svg';

export default function QuestionsAndAnswers() {
	const { courseSecureId, classeSecureId, lessonSecureId } = useLocalSearchParams()
	const formAddRef = useRef<Modalize>(null);
	const [onlyMine, setOnlyMine] = useState(false)
	const { data: dataQuestions, hasNextPage, fetchNextPage, isLoading, isFetchingNextPage } = useQuestionAndAnswer({
		limit: 5,
		lessonSecureId,
		onlyMine
	} as {
		limit: number;
		lessonSecureId: string;
		onlyMine: boolean
	})

	const Header = () => (
		<VStack>
			<Text
				textAlign="center"
				fontSize="md"
				fontWeight="medium"
				color="#00436C"
			>
				Perguntas e Respostas
			</Text>

			<HStack
				justifyContent="space-between"
				alignItems="center"
				mb={4}
			>
				<Checkbox
					value="true"
					defaultIsChecked={onlyMine}
					onChange={() => setOnlyMine(!onlyMine)}
					colorScheme="darkBlue"
				>
					<Text
						fontSize="xs"
					>
						Apenas minhas perguntas
					</Text>
				</Checkbox>

				<IconButton
					colorScheme="blueGray"
					icon={<Icon as={Entypo} name="plus" />}
					_icon={{
						color: "blueGray.900",
						size: "md"
					}}
					onPress={() => {
						formAddRef.current?.open();
					}}
				/>
			</HStack>
		</VStack>
	)

	return (
		<VStack flex={1}>
			{dataQuestions && dataQuestions.pages[0].questions.length
				? (<FlatList
					ListHeaderComponent={Header}
					p={5}
					data={!isFetchingNextPage ? dataQuestions.pages.flatMap(obj => obj.questions) : []}
					keyExtractor={(item) => item.secure_id}
					showsHorizontalScrollIndicator={false}
					ItemSeparatorComponent={() => <View h={5} />}
					ListFooterComponent={isFetchingNextPage ? <Spinner color="indigo.500" /> : undefined}
					onScrollEndDrag={() => {
						if (hasNextPage) {
							fetchNextPage()
						}
					}}
					renderItem={({ item }) => <Question key={item.secure_id} data={item} />}
				/>)
				: isLoading ? <Spinner color="indigo.500" /> : (
					<VStack flex={1} p={4}>
						<Header />
						<Center flex={1}>
							<QuestionSVG />
							<Text fontSize="xl" color="#00436C">Sem perguntas</Text>
							<Text fontSize="md" color="blueGray.500">Seja o primeiro a perguntar!</Text>
						</Center>
					</VStack>
				)
			}
			<Portal>
				<Modalize
					ref={formAddRef}
					modalTopOffset={50}
					adjustToContentHeight
				>
					<FormNewQuestion
						formAddRef={formAddRef}
						// courseSecureId={courseSecureId}
						// classeSecureId={classeSecureId}
						lessonSecureId={lessonSecureId}
					/>
				</Modalize>
			</Portal>
		</VStack>
	)
}
