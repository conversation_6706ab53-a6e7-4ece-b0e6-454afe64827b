import { Box, HStack, Icon, Text, VStack, useDisclose } from "native-base";
import { Pressable } from "react-native";
import { LessonStudent } from "src/@types/myCourses";
import ContentVideo from "./contentVideo";
import { Ionicons } from "@expo/vector-icons";
import ContentPage from "./contentPage";
import ContentScorm from "./contentScorm";

type LessonProps = {
  data: LessonStudent;
};

export default function Lesson({ data }: LessonProps) {
  const { isOpen, onToggle } = useDisclose();

  return (
    <Box bgColor="gray.200" p={3} rounded="md" shadow={1}>
      <Pressable onPress={onToggle}>
        <HStack justifyContent="space-between" alignItems="center">
          <HStack alignItems="center" space={2}>
            <Box
              size="6"
              bgColor="gray.300"
              rounded="full"
              justifyContent="center"
              alignItems="center"
            >
              {data.blocked ? (
                <Icon
                  as={Ionicons}
                  name="lock-closed-outline"
                  color="gray.800"
                  size="sm"
                />
              ) : data.completed ? (
                <Icon
                  as={Ionicons}
                  name="ios-checkmark-sharp"
                  color="green.800"
                  size="sm"
                />
              ) : null}
            </Box>
            <Text fontWeight="semibold" fontSize="sm" color="#00436C">
              {data.title}
            </Text>
          </HStack>
          <Icon
            as={Ionicons}
            name={isOpen ? "chevron-up" : "chevron-down"}
            color="gray.500"
            size="md"
          />
        </HStack>
      </Pressable>
      {isOpen && (
        <VStack space={4} mt={4}>
          {data.videos &&
            data.videos.map((item) => (
              <ContentVideo key={item.secure_id} data={item} />
            ))}
          {data.scorms &&
            data.scorms.map((item) => (
              <ContentScorm key={item.secure_id} data={item} />
            ))}
          {data.pdfs && (
            <ContentPage type="pdf" lessonSecureId={data.secure_id} />
          )}
          {data.audios && (
            <ContentPage type="audio" lessonSecureId={data.secure_id} />
          )}
          {data.complementaries && (
            <ContentPage type="complementary" lessonSecureId={data.secure_id} />
          )}
          {data.lessonActivities && (
            <ContentPage type="activitie" lessonSecureId={data.secure_id} />
          )}
          {data.lessonResearchs && (
            <ContentPage type="research" lessonSecureId={data.secure_id} />
          )}
          <ContentPage
            type="questionsAndAnswers"
            lessonSecureId={data.secure_id}
          />
        </VStack>
      )}
    </Box>
  );
}
