import { AntDesign, EvilIcons, Foundation, Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { Box, HStack, Icon, Text } from "native-base";
import { Pressable } from "react-native";


type ContentPageProps = {
	type: 'pdf' | 'audio' | 'scorm' | 'complementary' | 'activitie' | 'research' | 'questionsAndAnswers';
	lessonSecureId: string;
}

export default function ContentPage({ type, lessonSecureId }: ContentPageProps) {
	const getType = () => {
		switch (type) {
			case 'pdf':
				return {
					iconLib: AntDesign,
					iconName: "pdffile1",
					label: "Ver PDFs"
				}

			case 'audio':
				return {
					iconLib: AntDesign,
					iconName: "pdffile1",
					label: "Ver Audios"
				}

			case 'scorm':
				return {
					iconLib: Foundation,
					iconName: "play-video",
					label: "Ver Scorm"
				}

			case 'activitie':
				return {
					iconLib: EvilIcons,
					iconName: "pencil",
					label: "Ver Atividades"
				}

			case 'research':
				return {
					iconLib: AntDesign,
					iconName: "search1",
					label: "Ver Pesquisas"
				}

			case 'questionsAndAnswers':
				return {
					iconLib: Ionicons,
					iconName: "chatbubbles-outline",
					label: "Ver Perguntas e Respostas"
				}

			default:
				return {
					iconLib: Ionicons,
					iconName: "ios-attach",
					label: "Ver Material Complementar"
				}
		}
	}

	const handleContentsList = () => {
		const params = {
			lessonSecureId: lessonSecureId,
		}

		router.push({
			pathname: `/course/lessons/${type}`,
			params
		})
	}

	return (
		<Pressable onPress={handleContentsList}>
			<Box
				bgColor="blueGray.100"
				p={2}
				rounded="md"
				shadow={1}
			>
				<HStack space={1}>
					<Icon as={getType().iconLib} name={getType().iconName} color="#00436C" size="lg" />
					<Text
						fontSize="sm"
						color="#00436C"
					>
						{getType().label}
					</Text>
				</HStack>
			</Box>
		</Pressable>
	)
}
