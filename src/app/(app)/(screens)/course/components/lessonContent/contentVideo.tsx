import { Ionicons } from "@expo/vector-icons";
import { api } from "@services/api";
import { queryClient } from "@services/queryClient";
import { router } from "expo-router";
import {
  Box,
  HStack,
  Icon,
  Switch,
  Text,
  Pressable,
  Container,
} from "native-base";
import { LessonContentStudent } from "src/@types/myCourses";

type ContentVideoProps = {
  data: LessonContentStudent;
};

export default function ContentVideo({ data }: ContentVideoProps) {
  const handlePlayer = () => {
    const params = {
      title: data.title,
      excerpt: data.excerpt ? data.excerpt : "",
      description: data.description ? data.description : "",
      url: data.upload.url,
      secure_id: data.secure_id,
      statusContentFinished: (
        !!(data.userStatusContent && data.userStatusContent.finished)
      ).toString()
    };

    router.push({
      pathname: `/playerVideo`,
      params,
    });
  };

  async function handleStatus() {
    try {
      const status =
        !data.userStatusContent || !data.userStatusContent.finished
          ? true
          : false;
      await api.post(`v1/students/status-lesson-content`, {
        lessonContentSelected: data.secure_id,
        finished: status,
      });
      queryClient.invalidateQueries(["MyCourseContentStudent"]);
    } catch {}
  }

  return (
    <Box bgColor="blueGray.100" rounded="md" shadow={1}>
      <HStack justifyContent="space-between" alignItems="center" width="full">
        <Container width="100%">
          <Pressable onPress={handlePlayer} width="100%">
            <HStack width="100%" p={2}>
              <Icon
                as={Ionicons}
                name="play-outline"
                color="#00436C"
                size="lg"
              />
              <Text fontSize="sm" color="#00436C">
                {data.title}
              </Text>
            </HStack>
          </Pressable>
        </Container>

        <Switch
          size={"sm"}
          isChecked={data.userStatusContent && data.userStatusContent.finished}
          colorScheme="darkBlue"
          onChange={() => handleStatus()}
        />
      </HStack>
    </Box>
  );
}
