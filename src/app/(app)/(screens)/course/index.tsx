import { useLocalSearchParams } from "expo-router"
import { AspectRatio, Box, Image, ScrollView, VStack } from "native-base"
import Lesson from "./components/lessonContent"
import { useCourseContent } from "@contexts/course"
import { useEffect } from "react"


type CourseProps = {
	courseSecureId: string
	subscriptionSecureId: string
}

export default function Course() {
	const { courseSecureId, subscriptionSecureId } = useLocalSearchParams<CourseProps>()
	const { course, setSubscription, isLoading } = useCourseContent()

	useEffect(() => {
		setSubscription({ courseSecureId, subscriptionSecureId })
	}, [])

	if (isLoading || !course?.secure_id) {
		return <></>
	}

	return (
		<ScrollView
			showsVerticalScrollIndicator={false}
		>
			<VStack p={4}>
				<Box
					bgColor="white"
					shadow={2}
					rounded="xl"
				>
					<AspectRatio
						ratio={{
							base: 16 / 9
						}}
					>

						<Image
							source={{ uri: course.thumb.url }}
							alt="Imagem de Fundo"
							resizeMode="cover"
							rounded="xl"
						/>

					</AspectRatio>
				</Box>
				<VStack space={4} mt={4}>
					{course.lessons && course.lessons.map(item => <Lesson
						key={item.secure_id}
						data={item}
					/>)}
				</VStack>
			</VStack>
		</ScrollView>
	)
}
