import { A } from "@expo/html-elements"
import { api } from "@services/api"
import { queryClient } from "@services/queryClient"
import { Video, ResizeMode, Audio } from "expo-av"
import { useLocalSearchParams } from "expo-router"
import { AspectRatio, Box, ScrollView, Text, VStack } from "native-base"
import { useEffect, useRef, useState } from "react"
import { Platform } from "react-native"

type VideoStatsProps = {
	currentTimeVideo: number;
	totalTimeVideo?: number | null;
	videoEnded: boolean;
	percent: number;
}

type StatusProps = {
	positionMillis: number;
	durationMillis: number;
	isLoaded: boolean;
	isPlaying: boolean
}
export default function PlayerVideo() {
	const videoRef = useRef(null)
	const [status, setStatus] = useState<StatusProps>()
	const [minute, setMinute] = useState(0);
	const { title, excerpt, description, url, secure_id, statusContentFinished } = useLocalSearchParams()

	const statusContentFinishedBoolean = statusContentFinished === "true"

	const triggerAudio = async (ref) => {
		if (Platform.OS === 'ios') {
			await Audio.setAudioModeAsync({ playsInSilentModeIOS: true });
		}
	};

	useEffect(() => {
		if (status && status.isPlaying) triggerAudio(videoRef);
	}, [videoRef, status]);

	useEffect(() => {
		const interval = setInterval(() => {
			setMinute(prevMinute => prevMinute + 1);
		}, 1000);

		return () => {
			clearInterval(interval);
		};
	}, []);


	useEffect(() => {
		if (status) {
			const { positionMillis, durationMillis } = status
			const percent = Math.ceil((positionMillis / durationMillis) * 100)
			const seconds = Math.floor((durationMillis / 1000) - (positionMillis / 1000))
			const videoEnded = seconds <= 30 ? true : false
			const currentTimeVideo = Math.floor(positionMillis / 1000)

			if (videoEnded || minute > 0 && minute % 60 === 0) {
				api.post(`v1/students/status-lesson-content`, {
					lessonContentSelected: secure_id,
					finished: statusContentFinishedBoolean ? true : videoEnded,
					currentTime: currentTimeVideo,
					percentFinished: percent
				})
				queryClient.invalidateQueries(['MyCourseContentStudent'])
			}
		}
	}, [status])

	return (
		<VStack flex={1}>
			<AspectRatio ratio={16 / 9}>
				<Box flex={1} bgColor="white" shadow={2}>
					<Video
						ref={videoRef}
						style={{ flex: 1 }}
						resizeMode={ResizeMode.CONTAIN}
						source={{
							uri: url as string,
						}}
						useNativeControls


						//@ts-expect-error
						onPlaybackStatusUpdate={statusPlay => setStatus(() => statusPlay)}
					/>
				</Box>
			</AspectRatio>
			<ScrollView >
				<VStack space={1} p={4} flex={1}>
					{title && <Text
						fontSize="lg"
						fontWeight="semibold"
						color="#00436C"
					>
						{title}
					</Text>}
					{excerpt && <Text
						fontSize="md"
						color="#00436C"
					>{excerpt}
					</Text>}
					{description && <Text
						color="dark#00436C"
					>
						{description}
					</Text>}
				</VStack>
			</ScrollView>
		</VStack>
	)
}
