import { useLocalSearchParams } from "expo-router";
import { Box, Icon, IconButton } from "native-base";
import { AntDesign } from '@expo/vector-icons';
import { Dimensions, Linking } from "react-native";
import WebView from "react-native-webview";



export default function PdfView() {
	const { url } = useLocalSearchParams()
	const { width, height } = Dimensions.get('window');

	const handleDownload = (url) => {
		Linking.openURL(url)
	}

	const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>PDF Viewer</title>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.5.207/pdf.min.js"></script>
      <style>
        body {
          margin: 0;
          padding: 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          overflow: hidden;
        }
        #canvasContainer {
          width: 100%;
					height: 100vh;
          overflow: auto;
        }
        canvas {
          border: 1px solid black;
          margin-bottom: 10px;
        }
        .error {
          color: red;
          font-size: 16px;
          text-align: center;
          margin-top: 20px;
        }
      </style>
    </head>
    <body>
      <div id="canvasContainer"></div>
      <div id="errorMessage" class="error"></div>
      <script>
        const url = '${url}';
        const loadingTask = pdfjsLib.getDocument(url);

        loadingTask.promise.then(pdf => {
          const container = document.getElementById('canvasContainer');
          const numPages = pdf.numPages;

          for (let pageNum = 1; pageNum <= numPages; pageNum++) {
            pdf.getPage(pageNum).then(page => {
              const viewport = page.getViewport({ scale: 1 });
              const desiredWidth = window.innerWidth;
              const scaleToFit = desiredWidth / viewport.width;
              const fittedViewport = page.getViewport({ scale: scaleToFit });

              const canvas = document.createElement('canvas');
              const context = canvas.getContext('2d');
              canvas.height = fittedViewport.height;
              canvas.width = fittedViewport.width;

              container.appendChild(canvas);

              const renderContext = {
                canvasContext: context,
                viewport: fittedViewport
              };

              page.render(renderContext).promise.catch(renderError => {
                console.error('Error rendering page:', renderError);
                document.getElementById('errorMessage').innerText = 'Erro ao renderizar a página do PDF. ' + renderError.message;
              });
            }).catch(pageError => {
              console.error('Error getting page:', pageError);
              document.getElementById('errorMessage').innerText = 'Erro ao obter a página do PDF. ' + pageError.message;
            });
          }
        }).catch(loadingError => {
          console.error('Error loading PDF:', loadingError);
          document.getElementById('errorMessage').innerText = 'Erro ao carregar o PDF. Verifique se o arquivo é válido e tente novamente. ' + loadingError.message;
        });
      </script>
    </body>
    </html>
  `;

	return (
		<Box flex={1}>
			<WebView
				style={{ flex: 1 }}
				originWhitelist={['*']}
				source={{ html: htmlContent }}
			/>
			<IconButton
				position='absolute'
				right={2}
				colorScheme="blue"
				icon={<Icon as={AntDesign} name="clouddownload" />}
				_icon={{
					color: "#00436C",
					size: "lg"
				}}
				onPress={() => handleDownload(url)}
			/>
		</Box>
	);
}
