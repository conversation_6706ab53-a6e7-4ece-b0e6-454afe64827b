import {
	format,
	parseISO,
	differenceInYears,
	differenceInMonths,
	differenceInDays,
	differenceInHours,
	differenceInMinutes,
	differenceInSeconds,
} from 'date-fns';
import ptBR from 'date-fns/locale/pt-BR';

export function FormatDateAsHour(date?: string) {
	const newDate = !!date ? date.replace(/\s+/g, 'T') : String(new Date())

	return format(
		parseISO(newDate),
		"dd/MM/yy 'às' HH:mm",
		{
			locale: ptBR,
		}
	)
}

export const FormatDateForYearMonthDay = (date: string) => {
	return format(parseISO(date), "yyyy-MM-dd")
}

export const FormatDateForHourMinutes = (date: string) => {
	return format(parseISO(date), "HH:mm")
}

export const FormatDateForDayMonthYearUsingBars = (date?: string) => {
	const newDate = !!date ? date.replace(/\s+/g, 'T') : String(new Date())
	return format(parseISO(newDate), "dd/MM/yyyy")
}

export const FormatDateForYearMonthDayHoursSeconds = (date: string) => {
	return format(parseISO(date), "yyyy-MM-dd HH:mm:ss", { locale: ptBR })
}

export const FormatDateForYearMonthDayHoursMinutes = (date: string) => {
	return format(parseISO(date), "yyyy-MM-dd HH:mm", { locale: ptBR })
}

export function getDateDifference(date: string) {
	const inYear = differenceInYears(new Date(), parseISO(date))
	const inMonth = differenceInMonths(new Date(), parseISO(date))
	const inDays = differenceInDays(new Date(), parseISO(date))
	const inHours = differenceInHours(new Date(), parseISO(date))
	const inMinutes = differenceInMinutes(new Date(), parseISO(date))
	const inSeconds = differenceInSeconds(new Date(), parseISO(date))
	if (inYear > 0) {
		if (inYear === 1) {
			return `há ${inYear} ano`
		}
		return `há ${inYear} anos`
	}

	if (inMonth > 0) {
		if (inMonth === 1) {
			return `há ${inMonth} mês`
		}
		return `há ${inMonth} meses`
	}

	if (inDays > 0) {
		if (inDays === 1) {
			return `há ${inDays} dia`
		}
		return `há ${inDays} dias`
	}

	if (inHours > 0) {
		if (inHours === 1) {
			return `há ${inHours} hora`
		}
		return `há ${inHours} horas`
	}

	if (inMinutes > 0) {
		if (inMinutes === 1) {
			return `há ${inMinutes} minuto`
		}
		return `há ${inMinutes} minutos`
	}

	if (inSeconds > 0) {
		if (inSeconds === 1) {
			return `há ${inSeconds} segundo`
		}
		return `há ${inSeconds} segundos`
	}

	return 'agora'
}
