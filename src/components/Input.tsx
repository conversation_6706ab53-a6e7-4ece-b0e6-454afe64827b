import { Ionicons } from "@expo/vector-icons";
import { Stack, Input as InputBase, Text, IInputProps, Icon, Pressable, VStack } from "native-base";
import { useState } from "react";
import { Control, Controller, FieldError } from "react-hook-form";

type InputProps = IInputProps & {
	name: string;
	label?: string;
	placeholder?: string;
	password?: boolean;
	error?: FieldError;
	control: Control<any, any>;
	InputLeftElement?: any;
	InputRightElement?: any;
}

export function Input({ name, label, placeholder, control, error, password = false, InputLeftElement, InputRightElement, ...rest }: InputProps) {
	const [show, setShow] = useState(password ? true : false);
	return (
		<VStack space={1}>
			{label && (
				<Text color="#00436C">
					{label}
				</Text>
			)}
			<Controller
				control={control}
				name={name}
				render={({ field: { onChange, value } }) => (
					<InputBase
						placeholder={placeholder}
						value={value}
						onChangeText={onChange}
						placeholderTextColor="gray.400"
						color="#2c60a8"
						px={2}
						py={3}
						fontSize={15}
						borderWidth={1.5}
						borderColor="#00436C"
						backgroundColor="#F6F6F6"
						rounded={8}
						InputLeftElement={<Stack ml={2}>{InputLeftElement}</Stack>}
						InputRightElement={!password
							? InputRightElement
							: <Pressable onPress={() => setShow(!show)}>
								<Icon
									as={<Ionicons
										name={show ? "eye-off-outline" : "eye-outline"}
									/>}
									size={5}
									mr="2"
									color={"#00436C"}
								/>
							</Pressable>
						}
						_focus={{
							borderColor: "#2c60a8",
							color: "#204194"
						}}
						type={show ? 'password' : 'text'}
						// value={value}
						// onBlur={onBlur}
						// onChangeText={onChange}
						{...rest}
					/>
				)}
			/>
			{!!error && <Text color={`red.700`}>{error.message}</Text>}
		</VStack>
	)
}
