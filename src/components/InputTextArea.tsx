import { TextArea, Text, ITextAreaProps, VStack } from "native-base";
import { Control, Controller, FieldError } from "react-hook-form";

type InputProps = ITextAreaProps & {
	name: string;
	label?: string;
	placeholder?: string;
	password?: boolean;
	error?: FieldError;
	control: Control<any, any>;
	numberOfLines?: number
}

export function InputTextArea({ name, label, placeholder, control, error, numberOfLines, ...rest }: InputProps) {

	return (
		<VStack space={1}>
			{label && (
				<Text color="#00436C">
					{label}
				</Text>
			)}
			<Controller
				control={control}
				name={name}
				render={({ field: { onChange, value } }) => (
					<TextArea
						placeholder={placeholder}
						value={value}
						onChangeText={onChange}
						placeholderTextColor="gray.400"
						color="#2c60a8"
						px={2}
						py={3}
						fontSize={15}
						borderWidth={1.5}
						borderColor="#222F5B"
						backgroundColor="#F6F6F6"
						rounded={8}
						_focus={{
							borderColor: "#2c60a8",
							color: "#204194"
						}}
						numberOfLines={numberOfLines}
						autoCompleteType={false}
						{...rest}
					/>
				)}
			/>
			{!!error && <Text color={`red.700`}>{error.message}</Text>}
		</VStack>
	)
}
