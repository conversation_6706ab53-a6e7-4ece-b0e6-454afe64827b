import { useAuth } from "@contexts/auth";
import { Button, Flex, HStack, Icon, Text, VStack } from "native-base";
import { getVersion } from 'react-native-device-info';

import Logo from '@assets/logo_longevisar_slogan.svg';
import { Ionicons, AntDesign } from "@expo/vector-icons";
import { router } from "expo-router"

export default function NavigationDrawer() {
	const { signOut } = useAuth();
	const buildVersion = getVersion()

	const handleLibrary = () => {
		router.push({
			pathname: '/library',
		})
	}

	return (

		<VStack px={4} pt={16} pb={8} flex={1} justifyContent="space-between">
			<VStack>
				<Flex justifyContent="center" alignItems="center" mb={5}>
					<Logo width={150} height={65} />
				</Flex>
				<Button
					variant="ghost"
					colorScheme="darkBlue"
					onPress={() => handleLibrary()}
					justifyContent="left"
				>
					<HStack>
						<Icon
							as={<AntDesign
								name={"book"}
							/>}
							size={5}
							mr="2"
							color={"#00436C"}
						/><Text color={"#00436C"}>Biblioteca</Text>
					</HStack>
				</Button>
				<Button
					variant="ghost"
					colorScheme="darkBlue"
					onPress={() => signOut()}
					justifyContent="left"
				>
					<HStack>
						<Icon
							as={<Ionicons
								name={"exit-outline"}
							/>}
							size={5}
							mr="2"
							color={"#00436C"}
						/><Text color={"#00436C"}>Sair</Text>
					</HStack>
				</Button>
			</VStack>
			<Text fontSize="2xs" textAlign="center" color={"#00436C"}>By GroovZ - V. {buildVersion}</Text>
		</VStack>

	);

}
