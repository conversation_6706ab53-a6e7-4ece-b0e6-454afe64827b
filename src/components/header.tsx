import { HStack, Icon, IconButton, View } from "native-base";
import React from "react";
import { SafeAreaView, Platform } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { DrawerToggleButton } from "@react-navigation/drawer";
import { router } from "expo-router";
import { usePathname } from "expo-router";

import Logo from "@assets/logo_longevisar.svg";

type HeaderProps = any;

const Header: React.FC = (props: HeaderProps) => {
  const Pathname = usePathname();

  return (
    <SafeAreaView>
      <HStack
        mt={Platform.OS === "android" ? 5 : 0}
        p={2}
        alignItems="center"
        justifyContent="space-between"
        borderBottomWidth={1}
        borderBottomStyle="solid"
        borderBottomColor="gray.200"
      >
        <DrawerToggleButton tintColor="#222F5B" />
        <Logo width={115} height={45} />
        {Pathname !== "/home" ? (
          <IconButton
            colorScheme="blue"
            icon={<Icon as={Ionicons} name="chevron-back" />}
            _icon={{
              color: "#00436C",
              size: "lg",
            }}
            onPress={() => router.back()}
          />
        ) : (
          <View w={"44px"} />
        )}
      </HStack>
    </SafeAreaView>
  );
};

export default Header;
