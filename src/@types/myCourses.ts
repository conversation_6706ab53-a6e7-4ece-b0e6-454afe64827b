export type ThumbStudent = {
	secure_id: string
	url: string
}

export type UploadStudent = {
	secure_id: string
	url: string
}

export type UserRatingStudent = {
	secure_id: string
	rating: string
	depoiment?: string
}

export type LessonActivityChoiceStudent = {
	secure_id: string
	content: string
	is_correct: boolean
}

export type LessonActivityStudent = {
	secure_id: string
	question: string
	order: number
	type: 'essay' | 'choice' | 'selection_box' | 'selection_scale'
	lessonActivitieChoices: LessonActivityChoiceStudent[]
	userResponse?: {
		secure_id: string
		response: string
		status: 'unanswered' | 'answered_not_corrected' | 'answered_corrected' | 'finished'
		lessonActivitieChoice?: LessonActivityChoiceStudent
	}
}

export type LessonActivityCommentStudent = {
	secure_id: string
	comment: string
	user: {
		secure_id: string
		userInfo: {
			name: string
		}
	}
}

export type LessonResearchStudent = {
	active: boolean;
	created_at: string;
	publish_in_date: string;
	publish_in_days: null;
	publish_to_date: string;
	publish_to_days: null;
	search: {
		secure_id: string;
		title: string;
		description: string;
		created_at: string;
		updated_at: string;
	};
	secure_id: string;
	updated_at: string;
}

export type LessonContentStatusStudent = {
	secure_id: string
	current_time: number
	finished: boolean
	percent_finished: number
	lesson?: LessonStudent
	lessonContent?: LessonContentStudent
}

export type LessonContentStudent = {
	secure_id: string
	title: string
	slug: string
	excerpt?: string
	description?: string
	active?: boolean
	thumb?: ThumbStudent
	upload?: UploadStudent
	order?: number
	embed?: string
	external_url?: string;
	count_certificate: boolean;
	type?: 'pdf' | 'audio' | 'video' | 'image' | 'scorm' | 'complementary';
	source: 'upload' | 'vimeo' | 'youtube' | 'external';
	lessonContentDependence?: {
		secure_id: string;
		title: string;
		type?: 'pdf' | 'audio' | 'video' | 'image' | 'scorm' | 'complementary';
		userStatusContent?: {
			finished: boolean;
		}
	}
	userStatusContent?: LessonContentStatusStudent
}

export type LessonStudent = {
	secure_id: string
	slug: string
	title: string
	active?: boolean
	excerpt?: string
	description?: string
	order?: number
	videos?: LessonContentStudent[]
	audios?: LessonContentStudent[]
	pdfs?: LessonContentStudent[]
	scorms?: LessonContentStudent[]
	lives?: LessonContentStudent[]
	complementaries?: LessonContentStudent[]
	thumb?: ThumbStudent
	completed?: boolean;
	lessonActivities: boolean;
	lessonResearchs: boolean;
	userRating?: UserRatingStudent
	blocked: boolean;
}

export type MyCourseStudent = {
	secure_id: string
	title: string
	slug: string
	active: boolean
	subscription?: string
	excerpt: string
	thumb?: ThumbStudent
	lastVideoUserViewed?: LessonContentStatusStudent
	lessons?: LessonStudent[]
	total?: number
	completed?: number
	percent?: number
	classeSecureId?: string;
	planSecureId?: string;
	userRating?: UserRatingStudent
	limit_access_to_classes: boolean;
	isAllowedGenerateCertificate: boolean;
}

export type ListMyCoursesStudent = {
	secure_id: string
	title: string
	slug: string
	order: number
	courses: MyCourseStudent[]
}

export type BannerHomeStudent = {
	secure_id: string;
	link: string;
	image: {
		url: string;
	}
}
