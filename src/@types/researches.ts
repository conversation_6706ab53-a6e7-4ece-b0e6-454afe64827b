export type ResearchProps = {
	secure_id: string;
	publish_in_date: string;
	publish_to_date: string;
	publish_in_days: any;
	publish_to_days: any;
	active: boolean;
	created_at: string;
	updated_at: string;
	search: {
		secure_id: string;
		title: string;
		description: string;
		created_at: string;
		updated_at: string;
	};
	leadSearches: {
		finished: boolean
	}[]
}

export type SurveyProps = {
	secureId: string;
	survey: {
		title: string;
		description: string;
		firstQuestion: string;
	}
	finished: boolean;
	type: 'link' | 'e-mail' | 'platform';
	leadId: string | null;
	startAt?: string | null;
	endAt?: string | null;
	leadSearchSecureId?: string | null;
}


export type QuestionSurveyProps = {
	secureId: string;
	currentQuestion: {
		secure_id: string;
		content: string;
		type: 'essay' | 'multiple_choices' | 'only_choice' | 'selection_scale';
		order: number;
		active: boolean;
		choices: any[];
		created_at: string;
		updated_at: string;
	},
	responses: {
		secure_id: string;
		response: string | null;
		scale: number | null;
		choice: {
			secure_id: string;
			content: string;
			created_at: string;
			updated_at: string;
		};
		created_at: string;
		updated_at: string;
	}[];
	previousQuestionSecureId: string | null,
	nextQuestionSecureId: '0f294271-9650-486a-aa07-dd0fc86bc2ad',
	leadSearchSecureId: string;
	total: number;
	currentQuestionIndex: number;
	isTheLastQuestion: boolean;
	finished: boolean;
	endAt: string | null;
}
