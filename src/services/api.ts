import axios, { AxiosInstance } from "axios";
import { storageAuthTokenGet } from "@storage/storageAuthToken";
import { AppError } from "@utils/AppError";

type SignOut = () => void;

type APIInstanceProps = AxiosInstance & {
  registerInterceptTokenManager: (signOut: SignOut) => () => void;
};
let baseURL;
let origin;
if (__DEV__) {
  // baseURL = 'http://**************:3333'
  // origin = 'https://localhost:3000'
  baseURL = "https://api.groovz.com.br";
  // origin = 'https://longevisar.groovz.com.br'
  origin = "https://longevisar.groovz.com.br";
} else {
  baseURL = "https://api.groovz.com.br";
  origin = "https://longevisar.groovz.com.br";
}

const api = axios.create({
  baseURL: baseURL,
  withCredentials: true,
  headers: {
    origin: origin,
  },
}) as APIInstanceProps;

api.registerInterceptTokenManager = (singOut) => {
  const interceptTokenManager = api.interceptors.response.use(
    (response) => response,
    async (requestError) => {
      const { token } = await storageAuthTokenGet();

      const originalRequestConfig = requestError.config;

      originalRequestConfig.headers = { Authorization: `Bearer ${token}` };
      api.defaults.headers.common["Authorization"] = `Bearer ${token}`;

      if (
        requestError.response.config.url !== "/v1/sessions" &&
        requestError.response?.status === 401
      ) {
        singOut();
        return Promise.reject(requestError);
      }

      if (requestError.response && requestError.response.data) {
        return Promise.reject(new AppError(requestError.response.data));
      } else {
        return Promise.reject(requestError);
      }
    }
  );

  return () => {
    api.interceptors.response.eject(interceptTokenManager);
  };
};

export { api };
