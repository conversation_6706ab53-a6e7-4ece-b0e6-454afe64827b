import { useCourse } from "@hooks/useCourse";
import { Dispatch, ReactNode, SetStateAction, createContext, useContext, useState } from "react"
import { MyCourseStudent } from "src/@types/myCourses";

type SubscriptionProps = {
	courseSecureId: string
	subscriptionSecureId: string
}

type CourseContextDataProps = {
	course?: MyCourseStudent
	setSubscription: Dispatch<SetStateAction<SubscriptionProps>>
	isLoading: boolean
}

type CourseProviderProps = {
	children: ReactNode
}

const CourseContext = createContext({} as CourseContextDataProps);

export function CourseProvider({ children }: CourseProviderProps) {
	const [subscription, setSubscription] = useState<SubscriptionProps>()

	const { data, isLoading } = useCourse({
		courseSecureId: subscription?.courseSecureId,
		subscriptionSecureId: subscription?.subscriptionSecureId
	})

	return (
		<CourseContext.Provider
			value={{
				setSubscription,
				course: { ...data?.course, subscription: subscription?.subscriptionSecureId },
				isLoading
			}}
		>
			{children}
		</CourseContext.Provider>
	)
}

export const useCourseContent = () => useContext(CourseContext)
