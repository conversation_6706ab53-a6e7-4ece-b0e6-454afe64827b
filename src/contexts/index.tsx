import { ReactNode } from "react";
import { AuthProvider } from "./auth";
import { CourseProvider } from "./course";
import { NativeBaseProvider } from "native-base";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from '@services/queryClient';

type Props = {
	children: ReactNode;
}

export const Provider: React.FC<Props> = ({ children }) => (
	<NativeBaseProvider>
		<QueryClientProvider client={queryClient}>
			<AuthProvider>
				<CourseProvider>
					{children}
				</CourseProvider>
			</AuthProvider>
		</QueryClientProvider>
	</NativeBaseProvider>
)
