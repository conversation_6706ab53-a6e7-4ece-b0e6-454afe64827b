PODS:
  - boost (1.84.0)
  - DoubleConversion (1.1.6)
  - EXAV (15.0.2):
    - ExpoModulesCore
    - ReactCommon/turbomodule/core
  - EXConstants (17.0.8):
    - ExpoModulesCore
  - Expo (52.0.47):
    - ExpoModulesCore
  - ExpoAsset (11.0.5):
    - ExpoModulesCore
  - ExpoFileSystem (18.0.12):
    - ExpoModulesCore
  - ExpoFont (13.0.4):
    - ExpoModulesCore
  - ExpoHead (4.0.21):
    - ExpoModulesCore
  - ExpoKeepAwake (14.0.3):
    - ExpoModulesCore
  - ExpoLinking (7.0.5):
    - ExpoModulesCore
  - ExpoModulesCore (2.2.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoSplashScreen (0.29.24):
    - ExpoModulesCore
  - fast_float (6.1.4)
  - FBLazyVector (0.76.9)
  - fmt (11.0.2)
  - glog (0.3.5)
  - hermes-engine (0.76.9):
    - hermes-engine/Pre-built (= 0.76.9)
  - hermes-engine/Pre-built (0.76.9)
  - OneSignalXCFramework (5.2.14):
    - OneSignalXCFramework/OneSignalComplete (= 5.2.14)
  - OneSignalXCFramework/OneSignal (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalLiveActivities
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalComplete (5.2.14):
    - OneSignalXCFramework/OneSignal
    - OneSignalXCFramework/OneSignalInAppMessages
    - OneSignalXCFramework/OneSignalLocation
  - OneSignalXCFramework/OneSignalCore (5.2.14)
  - OneSignalXCFramework/OneSignalExtension (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalInAppMessages (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLiveActivities (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLocation (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalNotifications (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOSCore (5.2.14):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalOutcomes (5.2.14):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalUser (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
  - RCT-Folly (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly/Default (= 2024.10.14.00)
  - RCT-Folly/Default (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
  - RCT-Folly/Fabric (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
  - RCTDeprecation (0.76.9)
  - RCTRequired (0.76.9)
  - RCTTypeSafety (0.76.9):
    - FBLazyVector (= 0.76.9)
    - RCTRequired (= 0.76.9)
    - React-Core (= 0.76.9)
  - React (0.76.9):
    - React-Core (= 0.76.9)
    - React-Core/DevSupport (= 0.76.9)
    - React-Core/RCTWebSocket (= 0.76.9)
    - React-RCTActionSheet (= 0.76.9)
    - React-RCTAnimation (= 0.76.9)
    - React-RCTBlob (= 0.76.9)
    - React-RCTImage (= 0.76.9)
    - React-RCTLinking (= 0.76.9)
    - React-RCTNetwork (= 0.76.9)
    - React-RCTSettings (= 0.76.9)
    - React-RCTText (= 0.76.9)
    - React-RCTVibration (= 0.76.9)
  - React-callinvoker (0.76.9)
  - React-Core (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/Default (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/DevSupport (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-Core/RCTWebSocket (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTWebSocket (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-CoreModules (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - RCT-Folly
    - RCTTypeSafety
    - React-Core/CoreModulesHeaders
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage
    - ReactCodegen
    - ReactCommon
    - SocketRocket
  - React-cxxreact (0.76.9):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-jsinspector
    - React-logger
    - React-perflogger
    - React-runtimeexecutor
    - React-timing
  - React-debug (0.76.9)
  - React-defaultsnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-domnativemodule
    - React-Fabric
    - React-featureflags
    - React-featureflagsnativemodule
    - React-graphics
    - React-idlecallbacksnativemodule
    - React-ImageManager
    - React-microtasksnativemodule
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-domnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.76.9)
    - React-Fabric/attributedstring (= 0.76.9)
    - React-Fabric/componentregistry (= 0.76.9)
    - React-Fabric/componentregistrynative (= 0.76.9)
    - React-Fabric/components (= 0.76.9)
    - React-Fabric/core (= 0.76.9)
    - React-Fabric/dom (= 0.76.9)
    - React-Fabric/imagemanager (= 0.76.9)
    - React-Fabric/leakchecker (= 0.76.9)
    - React-Fabric/mounting (= 0.76.9)
    - React-Fabric/observers (= 0.76.9)
    - React-Fabric/scheduler (= 0.76.9)
    - React-Fabric/telemetry (= 0.76.9)
    - React-Fabric/templateprocessor (= 0.76.9)
    - React-Fabric/uimanager (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.76.9)
    - React-Fabric/components/root (= 0.76.9)
    - React-Fabric/components/view (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/dom (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers/events (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager/consistency (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricComponents (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.76.9)
    - React-FabricComponents/textlayoutmanager (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.76.9)
    - React-FabricComponents/components/iostextinput (= 0.76.9)
    - React-FabricComponents/components/modal (= 0.76.9)
    - React-FabricComponents/components/rncore (= 0.76.9)
    - React-FabricComponents/components/safeareaview (= 0.76.9)
    - React-FabricComponents/components/scrollview (= 0.76.9)
    - React-FabricComponents/components/text (= 0.76.9)
    - React-FabricComponents/components/textinput (= 0.76.9)
    - React-FabricComponents/components/unimplementedview (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/iostextinput (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/modal (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/rncore (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/safeareaview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/scrollview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/text (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/textinput (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricImage (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.76.9)
  - React-featureflagsnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-graphics (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly/Fabric
    - React-jsi
    - React-jsiexecutor
    - React-utils
  - React-hermes (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimeexecutor
  - React-idlecallbacksnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-ImageManager (0.76.9):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-cxxreact
    - React-debug
    - React-jsi
  - React-jsi (0.76.9):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
  - React-jsiexecutor (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-perflogger
  - React-jsinspector (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - React-featureflags
    - React-jsi
    - React-perflogger
    - React-runtimeexecutor
  - React-jsitracing (0.76.9):
    - React-jsi
  - React-logger (0.76.9):
    - glog
  - React-Mapbuffer (0.76.9):
    - glog
    - React-debug
  - React-microtasksnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-onesignal (5.2.13):
    - OneSignalXCFramework (= 5.2.14)
    - React (< 1.0.0, >= 0.13.0)
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (4.12.0):
    - React-Core
  - react-native-webview (13.12.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-nativeconfig (0.76.9)
  - React-NativeModulesApple (0.76.9):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.76.9):
    - DoubleConversion
    - RCT-Folly (= 2024.10.14.00)
  - React-performancetimeline (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - React-cxxreact
    - React-timing
  - React-RCTActionSheet (0.76.9):
    - React-Core/RCTActionSheetHeaders (= 0.76.9)
  - React-RCTAnimation (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTAppDelegate (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon
  - React-RCTBlob (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTFabric (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-performancetimeline
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTLinking (0.76.9):
    - React-Core/RCTLinkingHeaders (= 0.76.9)
    - React-jsi (= 0.76.9)
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.76.9)
  - React-RCTNetwork (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTSettings (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTText (0.76.9):
    - React-Core/RCTTextHeaders (= 0.76.9)
    - Yoga
  - React-RCTVibration (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-rendererconsistency (0.76.9)
  - React-rendererdebug (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - RCT-Folly
    - React-debug
  - React-rncore (0.76.9)
  - React-RuntimeApple (0.76.9):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
  - React-RuntimeCore (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.76.9):
    - React-jsi (= 0.76.9)
  - React-RuntimeHermes (0.76.9):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
  - React-timing (0.76.9)
  - React-utils (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-debug
    - React-jsi (= 0.76.9)
  - ReactCodegen (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - ReactCommon (0.76.9):
    - ReactCommon/turbomodule (= 0.76.9)
  - ReactCommon/turbomodule (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-jsi
    - React-logger
    - React-perflogger
    - ReactCommon/turbomodule/bridging (= 0.76.9)
    - ReactCommon/turbomodule/core (= 0.76.9)
  - ReactCommon/turbomodule/bridging (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-jsi (= 0.76.9)
    - React-logger
    - React-perflogger
  - ReactCommon/turbomodule/core (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-debug (= 0.76.9)
    - React-featureflags (= 0.76.9)
    - React-jsi
    - React-logger
    - React-perflogger
    - React-utils (= 0.76.9)
  - RNCAsyncStorage (1.23.1):
    - React-Core
  - RNDeviceInfo (11.1.0):
    - React-Core
  - RNGestureHandler (2.20.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.16.7)
    - RNReanimated/worklets (= 3.16.7)
    - Yoga
  - RNReanimated/reanimated (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.16.7)
    - Yoga
  - RNReanimated/reanimated/apple (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated/worklets (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (4.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNSVG (15.8.0):
    - React-Core
  - SocketRocket (0.7.1)
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXAV (from `../node_modules/expo-av/ios`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - Expo (from `../node_modules/expo`)
  - ExpoAsset (from `../node_modules/expo-asset/ios`)
  - ExpoFileSystem (from `../node_modules/expo-file-system/ios`)
  - ExpoFont (from `../node_modules/expo-font/ios`)
  - ExpoHead (from `../node_modules/expo-router/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoLinking (from `../node_modules/expo-linking/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - ExpoSplashScreen (from `../node_modules/expo-splash-screen/ios`)
  - fast_float (from `../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-onesignal (from `../node_modules/react-native-onesignal`)
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - OneSignalXCFramework
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXAV:
    :path: "../node_modules/expo-av/ios"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  Expo:
    :path: "../node_modules/expo"
  ExpoAsset:
    :path: "../node_modules/expo-asset/ios"
  ExpoFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../node_modules/expo-font/ios"
  ExpoHead:
    :path: "../node_modules/expo-router/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoLinking:
    :path: "../node_modules/expo-linking/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  ExpoSplashScreen:
    :path: "../node_modules/expo-splash-screen/ios"
  fast_float:
    :podspec: "../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-11-12-RNv0.76.2-5b4aa20c719830dcf5684832b89a6edb95ac3d64
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-onesignal:
    :path: "../node_modules/react-native-onesignal"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 1dca942403ed9342f98334bf4c3621f011aa7946
  DoubleConversion: f16ae600a246532c4020132d54af21d0ddb2a385
  EXAV: 4c41f0a6ae54a0b96597abc90e37962ab2ee0d85
  EXConstants: fcfc75800824ac2d5c592b5bc74130bad17b146b
  Expo: 1687edb10c76b0c0f135306d6ae245379f50ed54
  ExpoAsset: 48386d40d53a8c1738929b3ed509bcad595b5516
  ExpoFileSystem: 42d363d3b96f9afab980dcef60d5657a4443c655
  ExpoFont: f354e926f8feae5e831ec8087f36652b44a0b188
  ExpoHead: df924203fbf8e0913fc38b0f6aec71f9a9115482
  ExpoKeepAwake: b0171a73665bfcefcfcc311742a72a956e6aa680
  ExpoLinking: 8d12bee174ba0cdf31239706578e29e74a417402
  ExpoModulesCore: 725faec070d590810d2ea5983d9f78f7cf6a38ec
  ExpoSplashScreen: 399ee9f85b6c8a61b965e13a1ecff8384db591c2
  fast_float: 06eeec4fe712a76acc9376682e4808b05ce978b6
  FBLazyVector: 7605ea4810e0e10ae4815292433c09bf4324ba45
  fmt: 01b82d4ca6470831d1cc0852a1af644be019e8f6
  glog: 08b301085f15bcbb6ff8632a8ebaf239aae04e6a
  hermes-engine: 9e868dc7be781364296d6ee2f56d0c1a9ef0bb11
  OneSignalXCFramework: 7112f3e89563e41ebc23fe807788f11985ac541c
  RCT-Folly: ea9d9256ba7f9322ef911169a9f696e5857b9e17
  RCTDeprecation: ebe712bb05077934b16c6bf25228bdec34b64f83
  RCTRequired: ca91e5dd26b64f577b528044c962baf171c6b716
  RCTTypeSafety: e7678bd60850ca5a41df9b8dc7154638cb66871f
  React: 4641770499c39f45d4e7cde1eba30e081f9d8a3d
  React-callinvoker: 4bef67b5c7f3f68db5929ab6a4d44b8a002998ea
  React-Core: a68cea3e762814e60ecc3fa521c7f14c36c99245
  React-CoreModules: d81b1eaf8066add66299bab9d23c9f00c9484c7c
  React-cxxreact: 984f8b1feeca37181d4e95301fcd6f5f6501c6ab
  React-debug: 817160c07dc8d24d020fbd1eac7b3558ffc08964
  React-defaultsnativemodule: 21f216e8db975897eb32b5f13247f5bbfaa97f41
  React-domnativemodule: 19270ad4b8d33312838d257f24731a0026809d49
  React-Fabric: f6dade7007533daeb785ba5925039d83f343be4b
  React-FabricComponents: b0655cc3e1b5ae12a4a1119aa7d8308f0ad33520
  React-FabricImage: 9b157c4c01ac2bf433f834f0e1e5fe234113a576
  React-featureflags: f2792b067a351d86fdc7bec23db3b9a2f2c8d26c
  React-featureflagsnativemodule: 3a8731d8fd9f755be57e00d9fa8a7f92aa77e87d
  React-graphics: 68969e4e49d73f89da7abef4116c9b5f466aa121
  React-hermes: ac0bcba26a5d288ebc99b500e1097da2d0297ddf
  React-idlecallbacksnativemodule: 9a2c5b5c174c0c476f039bedc1b9497a8272133e
  React-ImageManager: e906eec93a9eb6102a06576b89d48d80a4683020
  React-jserrorhandler: ac5dde01104ff444e043cad8f574ca02756e20d6
  React-jsi: 496fa2b9d63b726aeb07d0ac800064617d71211d
  React-jsiexecutor: dd22ab48371b80f37a0a30d0e8915b6d0f43a893
  React-jsinspector: 4629ac376f5765e684d19064f2093e55c97fd086
  React-jsitracing: 7a1c9cd484248870cf660733cd3b8114d54c035f
  React-logger: c4052eb941cca9a097ef01b59543a656dc088559
  React-Mapbuffer: 33546a3ebefbccb8770c33a1f8a5554fa96a54de
  React-microtasksnativemodule: 5c3d795318c22ab8df55100e50b151384a4a60b3
  react-native-onesignal: 0829ce2a504db26dd80eaf14d4e0211ea626fe62
  react-native-render-html: 5afc4751f1a98621b3009432ef84c47019dcb2bd
  react-native-safe-area-context: 8b8404e70b0cbf2a56428a17017c14c1dcc16448
  react-native-webview: 69a5462ca94921ff695e1b52b12fffe62af7d312
  React-nativeconfig: 8efdb1ef1e9158c77098a93085438f7e7b463678
  React-NativeModulesApple: cebca2e5320a3d66e123cade23bd90a167ffce5e
  React-perflogger: 72e653eb3aba9122f9e57cf012d22d2486f33358
  React-performancetimeline: cd6a9374a72001165995d2ab632f672df04076dc
  React-RCTActionSheet: aacf2375084dea6e7c221f4a727e579f732ff342
  React-RCTAnimation: 395ab53fd064dff81507c15efb781c8684d9a585
  React-RCTAppDelegate: 1e5b43833e3e36e9fa34eec20be98174bc0e14a2
  React-RCTBlob: 13311e554c1a367de063c10ee7c5e6573b2dd1d6
  React-RCTFabric: bd906861a4e971e21d8df496c2d8f3ca6956f840
  React-RCTImage: 1b1f914bcc12187c49ba5d949dac38c2eb9f5cc8
  React-RCTLinking: 4ac7c42beb65e36fba0376f3498f3cd8dd0be7fa
  React-RCTNetwork: 938902773add4381e84426a7aa17a2414f5f94f7
  React-RCTSettings: e848f1ba17a7a18479cf5a31d28145f567da8223
  React-RCTText: 7e98fafdde7d29e888b80f0b35544e0cb07913cf
  React-RCTVibration: cd7d80affd97dc7afa62f9acd491419558b64b78
  React-rendererconsistency: b4917053ecbaa91469c67a4319701c9dc0d40be6
  React-rendererdebug: aa181c36dd6cf5b35511d1ed875d6638fd38f0ec
  React-rncore: 120d21715c9b4ba8f798bffe986cb769b988dd74
  React-RuntimeApple: d033becbbd1eba6f9f6e3af6f1893030ce203edd
  React-RuntimeCore: 38af280bb678e66ba000a3c3d42920b2a138eebb
  React-runtimeexecutor: 877596f82f5632d073e121cba2d2084b76a76899
  React-RuntimeHermes: 37aad735ff21ca6de2d8450a96de1afe9f86c385
  React-runtimescheduler: 8ec34cc885281a34696ea16c4fd86892d631f38d
  React-timing: 331cbf9f2668c67faddfd2e46bb7f41cbd9320b9
  React-utils: ed818f19ab445000d6b5c4efa9d462449326cc9f
  ReactCodegen: f853a20cc9125c5521c8766b4b49375fec20648b
  ReactCommon: 300d8d9c5cb1a6cd79a67cf5d8f91e4d477195f9
  RNCAsyncStorage: aa75595c1aefa18f868452091fa0c411a516ce11
  RNDeviceInfo: 900bd20e1fd3bfd894e7384cc4a83880c0341bd3
  RNGestureHandler: 32336458ef842088a459ccfcc10b583186de2633
  RNReanimated: 6d22a1aec06a9787b797bc0c40fc2779be0599a5
  RNScreens: 295d9c0aaeb7f680d03d7e9b476569a4959aae89
  RNSVG: 8542aa11770b27563714bbd8494a8436385fc85f
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Yoga: feb4910aba9742cfedc059e2b2902e22ffe9954a

PODFILE CHECKSUM: 9656bce040c2ceee2303975dc29cfc6ba9191aad

COCOAPODS: 1.16.2
